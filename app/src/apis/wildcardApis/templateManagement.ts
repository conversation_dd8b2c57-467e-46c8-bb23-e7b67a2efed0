import { WILDCARD_APIS } from "../ApiConstant";
import { API } from "../api";

const { TEMPLATE_MANAGEMENT } = WILDCARD_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};


// Function to get department detail
const getAllTemplates = (params:any) => {
  return API.get(`${TEMPLATE_MANAGEMENT}`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get department detail
const getTemplateDetail = (id: number) => {
  return API.get(`${TEMPLATE_MANAGEMENT}/${id}`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to update the status of a business
const updateTemplate = (id: number, data: any) => {
  return API.put(`${TEMPLATE_MANAGEMENT}/${id}`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
    updateTemplate,
    getTemplateDetail,
    getAllTemplates,
};
