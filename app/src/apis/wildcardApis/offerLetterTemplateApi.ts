import { WILDCARD_APIS } from "../ApiConstant";
import { API } from "../api";

const { OFFER_LETTER_TEMPLATE } = WILDCARD_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Function to get the list of offer letter templates
const getOfferLetterTemplateList = (params: any) => {
  return API.get(OFFER_LETTER_TEMPLATE, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to create a new offer letter template
const createOfferLetterTemplate = (params: any) => {
  return API.post(OFFER_LETTER_TEMPLATE, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get offer letter template detail
const getOfferLetterTemplateDetail = (id: number) => {
  return API.get(`${OFFER_LETTER_TEMPLATE}/${id}`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to update an offer letter template
const updateOfferLetterTemplate = (id: number, data: any) => {
  return API.put(`${OFFER_LETTER_TEMPLATE}/${id}`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to update the status of an offer letter template
const updateOfferLetterTemplateStatus = (id: number, data: any) => {
  return API.put(`${OFFER_LETTER_TEMPLATE}/${id}/status`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to delete an offer letter template
const deleteOfferLetterTemplate = (id: number) => {
  return API.delete(`${OFFER_LETTER_TEMPLATE}/${id}`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  getOfferLetterTemplateList,
  createOfferLetterTemplate,
  updateOfferLetterTemplate,
  updateOfferLetterTemplateStatus,
  getOfferLetterTemplateDetail,
  deleteOfferLetterTemplate,
};
