import React, { useState, useEffect } from 'react'
import { PrivateLayout } from "@src/components/Layout";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { GetServerSideProps } from "next";
import { KeyPairInterface } from "@src/redux/interfaces";
import { Button, Card, Form } from "react-bootstrap";
import { CustomEditor } from '@src/components/CustomEditor';
import { useAppDispatch } from "@src/redux/store";
import { openDialog } from "@src/redux/actions";
import DialogComponents from "@src/components/DialogComponents";
import { useRouter } from "next/router";
import VisibilityIcon from "@mui/icons-material/Visibility";
import SaveIcon from "@mui/icons-material/Save";



type TemplateManagementPageProps = {
  pageDetail: KeyPairInterface;
};

interface TemplateState {
  id?: number;
  name: string;
  subject?: string;
  position?: string;
  department?: string;
  type: 'email' | 'offer';
  content: string;
  status: string;
  created_by: string;
  created_at: string;
}

export default function TemplateManagementPage({
  pageDetail,
}: TemplateManagementPageProps) {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { id } = router.query;

  // Enhanced state for template data
  const [templateData, setTemplateData] = useState<TemplateState>({
    id: 1,
    name: "Sample Offer Letter Template",
    position: "Software Engineer",
    department: "Engineering",
    type: 'offer',
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #333;">{{company_name}}</h1>
          <h2 style="color: #666;">OFFER LETTER</h2>
        </div>

        <p>Dear {{candidate_name}},</p>

        <p>We are pleased to offer you the position of <strong>{{position}}</strong> in our {{department}} department at {{company_name}}.</p>

        <h3 style="color: #333;">Position Details:</h3>
        <ul>
          <li><strong>Position:</strong> {{position}}</li>
          <li><strong>Department:</strong> {{department}}</li>
          <li><strong>Reporting Manager:</strong> {{reporting_manager}}</li>
          <li><strong>Start Date:</strong> {{start_date}}</li>
        </ul>

        <h3 style="color: #333;">Compensation & Benefits:</h3>
        <ul>
          <li><strong>Annual Salary:</strong> {{salary}}</li>
          <li><strong>Benefits:</strong> {{benefits}}</li>
        </ul>

        <p>Please confirm your acceptance of this offer by signing and returning this letter by [Date].</p>

        <p>We look forward to welcoming you to our team!</p>

        <p>Sincerely,<br>
        {{company_name}} HR Team</p>

        <div style="margin-top: 50px;">
          <p>Candidate Signature: _________________________ Date: _________</p>
        </div>
      </div>
    `,
    status: "Active",
    created_by: "HR Manager",
    created_at: "December 15, 2024 11:00 AM"
  });

  const [errors, setErrors] = useState({
    name: "",
    subject: "",
    position: "",
    department: "",
    content: ""
  });

  // Handle preview functionality
  const handlePreview = () => {
    dispatch(
      openDialog({
        config: DialogComponents.TEMPLATE_PREVIEW_MODAL,
        options: {
          template: templateData,
          onEdit: () => {
            // Already in edit mode, just close the preview
            console.log("Already in edit mode");
          },
          onCopy: () => {
            handleDuplicate();
          },
        },
      })
    );
  };

  // Handle save functionality
  const handleSave = () => {
    // Validate required fields
    const newErrors = {
      name: templateData.name.trim() === "" ? "Template name is required" : "",
      subject: templateData.type === 'email' && templateData.subject?.trim() === "" ? "Subject is required for email templates" : "",
      position: templateData.type === 'offer' && templateData.position?.trim() === "" ? "Position is required for offer letter templates" : "",
      department: templateData.type === 'offer' && templateData.department?.trim() === "" ? "Department is required for offer letter templates" : "",
      content: templateData.content.trim() === "" ? "Template content is required" : ""
    };

    setErrors(newErrors);

    // Check if there are any errors
    const hasErrors = Object.values(newErrors).some(error => error !== "");

    if (!hasErrors) {
      // TODO: Implement actual save functionality
      console.log("Saving template:", templateData);
      // Show success message or redirect
    }
  };

  // Handle duplicate functionality
  const handleDuplicate = () => {
    const duplicatedTemplate = {
      ...templateData,
      id: undefined,
      name: `${templateData.name} (Copy)`,
      created_at: new Date().toLocaleString(),
      created_by: "Current User" // TODO: Get from auth state
    };

    // TODO: Implement actual duplicate functionality
    console.log("Duplicating template:", duplicatedTemplate);
  };

  // Handle input changes
  const handleInputChange = (field: keyof TemplateState, value: string) => {
    setTemplateData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field when user starts typing
    if (errors[field as keyof typeof errors]) {
      setErrors(prev => ({
        ...prev,
        [field]: ""
      }));
    }
  };

  return (
    <section className="">
      <div className="d-flex align-items-center flex-wrap flex-lg-nowrap gap-2 justify-content-between mb-3">
        <div className="bredcrubs d-flex gap-3 align-items-end">
          <h1 className="m-0 page-head">
            {pageDetail?.title ?? "Template Management"}
          </h1>
          <h4 className="m-0 page-head primary-clr position-relative ps-3">
            {templateData.type === 'email' ? 'Email Template' : 'Offer Letter Template'}
          </h4>
        </div>
      </div>

      <div className="box-content">
        <div className="row">
          <div className="col-lg-12">
            <Card>
              <Card.Body>
                {/* Template Basic Information */}
                <div className="mb-4">
                  <h5 className="fw-bold mb-3">
                    {templateData.type === 'email' ? 'Email Template Details' : 'Offer Letter Template Details'}
                  </h5>


                </div>

                {/* Template Content Editor */}
                <div className="mb-4">
                  <Form.Label className="fw-bold mb-3">
                    Template Content <span className="text-danger">*</span>
                  </Form.Label>
                  <div className="position-relative">
                    <CustomEditor
                      value={templateData.content}
                      onChange={(content) => handleInputChange('content', content)}
                    />
                    {errors.content && (
                      <div className="text-danger mt-2">{errors.content}</div>
                    )}
                  </div>
                </div>

                {/* Available Variables Help */}
                <div className="alert alert-info">
                  <h6 className="mb-2">Available Variables:</h6>
                  <div className="row">
                    {templateData.type === 'email' ? (
                      <>
                        <div className="col-md-6">
                          <small>
                            <code>{'{{candidate_name}}'}</code> - Candidate's name<br/>
                            <code>{'{{position}}'}</code> - Job position<br/>
                            <code>{'{{company_name}}'}</code> - Company name<br/>
                            <code>{'{{interview_date}}'}</code> - Interview date
                          </small>
                        </div>
                        <div className="col-md-6">
                          <small>
                            <code>{'{{interviewer_name}}'}</code> - Interviewer's name<br/>
                            <code>{'{{job_location}}'}</code> - Job location<br/>
                            <code>{'{{hr_contact}}'}</code> - HR contact info
                          </small>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="col-md-6">
                          <small>
                            <code>{'{{candidate_name}}'}</code> - Candidate's name<br/>
                            <code>{'{{position}}'}</code> - Job position<br/>
                            <code>{'{{salary}}'}</code> - Salary amount<br/>
                            <code>{'{{start_date}}'}</code> - Start date
                          </small>
                        </div>
                        <div className="col-md-6">
                          <small>
                            <code>{'{{company_name}}'}</code> - Company name<br/>
                            <code>{'{{department}}'}</code> - Department<br/>
                            <code>{'{{benefits}}'}</code> - Benefits package<br/>
                            <code>{'{{reporting_manager}}'}</code> - Manager name
                          </small>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </Card.Body>

              <Card.Footer>
                <div className="d-flex justify-content-between align-items-center">
                  <div className="text-muted small">
                    <strong>Status:</strong> {templateData.status} |
                    <strong> Created by:</strong> {templateData.created_by} |
                    <strong> Created:</strong> {templateData.created_at}
                  </div>
                  <div className="d-flex gap-2">
                    <Button
                      variant="outline-primary"
                      onClick={handlePreview}
                      className="d-flex align-items-center gap-2">
                      <VisibilityIcon fontSize="small" />
                      Preview
                    </Button>
                    <Button
                      variant="primary"
                      onClick={handleSave}
                      className="d-flex align-items-center gap-2">
                      <SaveIcon fontSize="small" />
                      Save
                    </Button>
                  </div>
                </div>
              </Card.Footer>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}

export const getServerSideProps: GetServerSideProps = async ({
  req,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    { wildcard: true },
    "template_management",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
      pageDetail: pageDetail,
    },
  };
};

TemplateManagementPage.layout = PrivateLayout;