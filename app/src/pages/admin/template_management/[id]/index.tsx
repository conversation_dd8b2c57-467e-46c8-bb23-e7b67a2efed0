import React, { useState, useEffect } from 'react'
import { PrivateLayout } from "@src/components/Layout";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { GetServerSideProps } from "next";
import { KeyPairInterface } from "@src/redux/interfaces";
import { Button, Card, Form } from "react-bootstrap";
import { CustomEditor } from '@src/components/CustomEditor';
import { useAppDispatch } from "@src/redux/store";
import { useSelector } from "react-redux";
import { openDialog, setLoader, getEmailTemplateDetail } from "@src/redux/actions";
import DialogComponents from "@src/components/DialogComponents";
import { useRouter } from "next/router";
import VisibilityIcon from "@mui/icons-material/Visibility";
import SaveIcon from "@mui/icons-material/Save";
import { emailTemplateApi } from "@src/apis/wildcardApis";
import flashMessage from "@src/components/FlashMessage";
import { RootState } from "@src/redux/reducers";



type TemplateManagementPageProps = {
  pageDetail: KeyPairInterface;
};

interface TemplateState {
  id?: number;
  name: string;
  subject?: string;
  position?: string;
  department?: string;
  type: 'email' | 'offer';
  content: string;
  status: string;
  created_by: string;
  created_at: string;
}

export default function TemplateManagementPage({
  pageDetail,
}: TemplateManagementPageProps) {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { id, type } = router.query;

  // Get email template from Redux store
  const emailTemplateState = useSelector((state: RootState) => state.emailTemplate);

  // Enhanced state for template data
  const [templateData, setTemplateData] = useState<TemplateState>({
    name: "",
    type: (type as 'email' | 'offer') || 'email',
    content: "",
    status: "Active",
    created_by: "System",
    created_at: ""
  });

  const [isLoading, setIsLoading] = useState(true);

  const [errors, setErrors] = useState({
    name: "",
    subject: "",
    position: "",
    department: "",
    content: ""
  });

  // Load template data on component mount
  useEffect(() => {
    const loadTemplate = async () => {
      if (id && typeof id === 'string') {
        try {
          setIsLoading(true);
          dispatch(setLoader(true));
          await dispatch(getEmailTemplateDetail(parseInt(id)));
          dispatch(setLoader(false));
        } catch (error) {
          console.error("Error loading template:", error);
          flashMessage("Failed to load template", "error");
          dispatch(setLoader(false));
        } finally {
          setIsLoading(false);
        }
      }
    };

    loadTemplate();
  }, [id, dispatch]);

  // Update local state when Redux state changes
  useEffect(() => {
    if (emailTemplateState.emailTemplate) {
      const template = emailTemplateState.emailTemplate;
      const isOfferTemplate = template.template_name.toLowerCase().includes('offer letter');

      setTemplateData({
        id: template.id,
        name: isOfferTemplate
          ? template.template_name.replace(/^Candidate Offer Letter - /, '')
          : template.template_name,
        type: isOfferTemplate ? 'offer' : 'email',
        content: template.email_body,
        status: template.status === 1 ? "Active" : "Inactive",
        created_by: "System", // TODO: Add created_by field to backend
        created_at: new Date(template.created_at).toLocaleString()
      });
    }
  }, [emailTemplateState.emailTemplate]);

  // Handle preview functionality
  const handlePreview = () => {
    dispatch(
      openDialog({
        config: DialogComponents.TEMPLATE_PREVIEW_MODAL,
        options: {
          template: templateData,
          onEdit: () => {
            // Already in edit mode, just close the preview
            console.log("Already in edit mode");
          },
          onCopy: () => {
            handleDuplicate();
          },
        },
      })
    );
  };

  // Handle save functionality
  const handleSave = async () => {
    // Validate required fields
    const newErrors = {
      name: templateData.name.trim() === "" ? "Template name is required" : "",
      subject: templateData.type === 'email' && templateData.subject?.trim() === "" ? "Subject is required for email templates" : "",
      position: templateData.type === 'offer' && templateData.position?.trim() === "" ? "Position is required for offer letter templates" : "",
      department: templateData.type === 'offer' && templateData.department?.trim() === "" ? "Department is required for offer letter templates" : "",
      content: templateData.content.trim() === "" ? "Template content is required" : ""
    };

    setErrors(newErrors);

    // Check if there are any errors
    const hasErrors = Object.values(newErrors).some(error => error !== "");

    if (!hasErrors && templateData.id) {
      try {
        dispatch(setLoader(true));

        // Prepare template name based on type
        let templateName = templateData.name.trim();
        if (templateData.type === 'offer') {
          // Add "Candidate Offer Letter" prefix for offer letter templates
          templateName = `Candidate Offer Letter - ${templateName}`;
        }

        // Prepare the API payload
        const payload = {
          template_name: templateName,
          email_body: templateData.content
        };

        const { success, ...response } = await emailTemplateApi.updateEmailTemplate(templateData.id, payload);

        if (success) {
          flashMessage("Template updated successfully!", "success");
          // Reload the template data
          await dispatch(getEmailTemplateDetail(templateData.id));
        } else {
          flashMessage(response.message || "Failed to update template", "error");
        }
      } catch (error) {
        console.error("Error updating template:", error);
        flashMessage("An error occurred while updating the template", "error");
      } finally {
        dispatch(setLoader(false));
      }
    }
  };

  // Handle duplicate functionality
  const handleDuplicate = () => {
    const duplicatedTemplate = {
      ...templateData,
      id: undefined,
      name: `${templateData.name} (Copy)`,
      created_at: new Date().toLocaleString(),
      created_by: "Current User" // TODO: Get from auth state
    };

    // TODO: Implement actual duplicate functionality
    console.log("Duplicating template:", duplicatedTemplate);
  };

  // Handle input changes
  const handleInputChange = (field: keyof TemplateState, value: string) => {
    setTemplateData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field when user starts typing
    if (errors[field as keyof typeof errors]) {
      setErrors(prev => ({
        ...prev,
        [field]: ""
      }));
    }
  };

  if (isLoading) {
    return (
      <section className="">
        <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
          <div className="spinner-border" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="">
      <div className="d-flex align-items-center flex-wrap flex-lg-nowrap gap-2 justify-content-between mb-3">
        <div className="bredcrubs d-flex gap-3 align-items-end">
          <h1 className="m-0 page-head">
            {pageDetail?.title ?? "Template Management"}
          </h1>
          <h4 className="m-0 page-head primary-clr position-relative ps-3">
            {templateData.type === 'email' ? 'Email Template' : 'Offer Letter Template'}
          </h4>
        </div>
      </div>

      <div className="box-content">
        <div className="row">
          <div className="col-lg-12">
            <Card>
              <Card.Body>
                {/* Template Basic Information */}
                <div className="mb-4">
                  <h5 className="fw-bold mb-3">
                    {templateData.type === 'email' ? 'Email Template Details' : 'Offer Letter Template Details'}
                  </h5>


                </div>

                {/* Template Content Editor */}
                <div className="mb-4">
                  <Form.Label className="fw-bold mb-3">
                    Template Content <span className="text-danger">*</span>
                  </Form.Label>
                  <div className="position-relative">
                    <CustomEditor
                      value={templateData.content}
                      onChange={(content) => handleInputChange('content', content)}
                    />
                    {errors.content && (
                      <div className="text-danger mt-2">{errors.content}</div>
                    )}
                  </div>
                </div>

                {/* Available Variables Help */}
                <div className="alert alert-info">
                  <h6 className="mb-2">Available Variables:</h6>
                  <div className="row">
                    {templateData.type === 'email' ? (
                      <>
                        <div className="col-md-6">
                          <small>
                            <code>{'{{candidate_name}}'}</code> - Candidate's name<br/>
                            <code>{'{{position}}'}</code> - Job position<br/>
                            <code>{'{{company_name}}'}</code> - Company name<br/>
                            <code>{'{{interview_date}}'}</code> - Interview date
                          </small>
                        </div>
                        <div className="col-md-6">
                          <small>
                            <code>{'{{interviewer_name}}'}</code> - Interviewer's name<br/>
                            <code>{'{{job_location}}'}</code> - Job location<br/>
                            <code>{'{{hr_contact}}'}</code> - HR contact info
                          </small>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="col-md-6">
                          <small>
                            <code>{'{{candidate_name}}'}</code> - Candidate's name<br/>
                            <code>{'{{position}}'}</code> - Job position<br/>
                            <code>{'{{salary}}'}</code> - Salary amount<br/>
                            <code>{'{{start_date}}'}</code> - Start date
                          </small>
                        </div>
                        <div className="col-md-6">
                          <small>
                            <code>{'{{company_name}}'}</code> - Company name<br/>
                            <code>{'{{department}}'}</code> - Department<br/>
                            <code>{'{{benefits}}'}</code> - Benefits package<br/>
                            <code>{'{{reporting_manager}}'}</code> - Manager name
                          </small>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </Card.Body>

              <Card.Footer>
                <div className="d-flex justify-content-between align-items-center">
                  <div className="text-muted small">
                    <strong>Status:</strong> {templateData.status} |
                    <strong> Created by:</strong> {templateData.created_by} |
                    <strong> Created:</strong> {templateData.created_at}
                  </div>
                  <div className="d-flex gap-2">
                    <Button
                      variant="outline-primary"
                      onClick={handlePreview}
                      className="d-flex align-items-center gap-2">
                      <VisibilityIcon fontSize="small" />
                      Preview
                    </Button>
                    <Button
                      variant="primary"
                      onClick={handleSave}
                      className="d-flex align-items-center gap-2">
                      <SaveIcon fontSize="small" />
                      Save
                    </Button>
                  </div>
                </div>
              </Card.Footer>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}

export const getServerSideProps: GetServerSideProps = async ({
  req,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    { wildcard: true },
    "template_management",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
      pageDetail: pageDetail,
    },
  };
};

TemplateManagementPage.layout = PrivateLayout;