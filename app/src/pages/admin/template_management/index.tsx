import React, { useCallback, useEffect, useState } from 'react'
import { PrivateLayout } from "@src/components/Layout";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { GetServerSideProps } from "next";
import PageHeader from "@src/components/PageHeader/PageHeader";
import { KeyPairInterface } from "@src/redux/interfaces";
import { Button } from "react-bootstrap";
import { Pagination } from "antd";
import Dropdown from "react-bootstrap/Dropdown";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { useAppDispatch } from "@src/redux/store";
import { openDialog, setLoader } from "@src/redux/actions";
import DialogComponents from "@src/components/DialogComponents";
import { useRouter } from "next/router";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { getAllTemplateList } from '@src/redux/slices/templatesManagement';

type TemplateManagementPageProps = {
  pageDetail: KeyPairInterface;
  subdomain: string;
};



export default function TemplateManagementPage({
  pageDetail,
  subdomain,
}: TemplateManagementPageProps) {
  const [activeTab, setActiveTab] = useState<'email' | 'offer'>('email');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const dispatch = useAppDispatch();
  const router = useRouter();

  // Get email templates from Redux store
  const emailTemplateState = useSelector((state: RootState) => state.templateManagement);
  
  useEffect(()=>{
    setCurrentPage(1)
  },[activeTab])
  

  // Templates are now filtered by the API, so we can use them directly
  const templates = emailTemplateState.rows;

  // Fetch the template data with pagination
  const fetchData = useCallback(
    async (page: number, limit: number, templateType?: string) => {
      try {
        dispatch(setLoader(true));
        const params: any = { page, limit };
        if (templateType) {
          params.template_type = templateType;
        }
        await dispatch(getAllTemplateList(params));
        dispatch(setLoader(false));
      } catch (err) {
        console.error("Error fetching template data:", err);
        dispatch(setLoader(false));
      }
    },
    [dispatch],
  );

  useEffect(() => {
    if (subdomain) {
      fetchData(currentPage, pageSize, activeTab);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subdomain, currentPage, activeTab]);

  const handleEditTemplate = (templateId: number, type: 'email' | 'offer') => {
    router.push(`/admin/template_management/${templateId}?type=${type}`);
  };

  const handleViewTemplate = (templateId: number, type: 'email' | 'offer') => {
    const template = templates.find((t: any) => t.id === templateId);
    if (template) {
      dispatch(
        openDialog({
          config: DialogComponents.TEMPLATE_PREVIEW_MODAL,
          options: {
            template: {
              id: template.id,
              name: template.template_name,
              content: template.email_body,
              type: type,
              status: template.status === 1 ? 'Active' : 'Inactive',
              updated_at: template.updated_at,
              created_by: 'System' // TODO: Add created_by field to backend
            }
          },
        })
      );
    }
  };



  return (
    <section className="template">
      <PageHeader
        pageTitle={pageDetail?.title ?? "Template Management"}
        pageDescription={
          pageDetail?.description ?? "Manage email templates and offer letter templates"
        }
      >

        <div className="box-content">
          {/* Add New Template Button */}
          {/* <div className="d-flex justify-content-end mb-3">
            <Button
              onClick={() => router.push(`/admin/template_management/new?type=${activeTab}`)}
              className="btn btn-theme">
               {activeTab == 'email' ? '+ Add Email Template' : '+ Add Offer Letter Template' }
            </Button>
          </div> */}

          {/* Tab Navigation */}
          <div className="mb-4">
            <nav className="nav nav-tabs">
              <button
                className={`nav-link ${activeTab === 'email' ? 'active' : ''}`}
                onClick={() => setActiveTab('email')}
                type="button">
                Email Templates
              </button>
              <button
                className={`nav-link ${activeTab === 'offer' ? 'active' : ''}`}
                onClick={() => setActiveTab('offer')}
                type="button">
                Offer Letter Templates
              </button>
            </nav>
          </div>

          {/* Email Templates Table */}
          {activeTab === 'email' && (
            <div className="tab-content">
              <div className="tab-pane fade show active">
                <div className={`table-responsive email-template-list ${templates.length > 0 ? "" : "no-records"}`}>
                  <table className="table table-hover dataTable" style={{ width: "100%" }}>
                    <thead>
                      <tr role="row">
                        <th className="mw-50px">Sr. No</th>
                        <th className="mw-150px">Template Name</th>
                        {/* <th className="mw-200px">{activeTab === 'email' ? 'Email Body' : 'Template Content'}</th> */}
                        <th className="mw-80px">Status</th>
                        <th className="mw-120px">Date & Time</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {templates.length > 0 ? (
                        templates.map((template: any, index: number) => (
                          <tr key={template.id}>
                            <th>{(currentPage - 1) * pageSize + index + 1}</th>
                            <td>
                                {template.template_name}
                            </td>
                            {/* <td className="query-30-chars">
                              <div dangerouslySetInnerHTML={{ __html: template.email_body?.substring(0, 100) + '...' }} />
                            </td> */}
                            <td>
                              <span className={`badge ${template.status === 1 ? 'bg-success' : 'bg-secondary'}`}>
                                {template.status === 1 ? 'Active' : 'Inactive'}
                              </span>
                            </td>
                            <td>{new Date(template.updated_at).toLocaleDateString()}</td>
                            <td>
                              <div className="template-action no-arrow">
                                <Dropdown>
                                  <Dropdown.Toggle
                                    id={`email-template-dropdown-${template.id}`}
                                    className="text-decoration-none"
                                    as="span"
                                    type="button"
                                    role="button"
                                    aria-haspopup="true"
                                    aria-expanded="false">
                                    <span className="text-black small">
                                      <MoreVertIcon />
                                    </span>
                                  </Dropdown.Toggle>
                                  <Dropdown.Menu
                                    className="dropdown-menu-right shadow animated--grow-in"
                                    aria-labelledby={`email-template-dropdown-${template.id}`}>
                                    <Dropdown.Item
                                      onClick={() => handleViewTemplate(template.id, 'email')}
                                      className="no-decoration">
                                      View Template
                                    </Dropdown.Item>
                                    <Dropdown.Item
                                      onClick={() => handleEditTemplate(template.id, 'email')}
                                      className="no-decoration">
                                      Edit Template
                                    </Dropdown.Item>
                                  </Dropdown.Menu>
                                </Dropdown>
                              </div>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr className="no-records">
                          <td colSpan={6} className="text-center">
                            <span>No {activeTab === 'email' ? 'Email' : 'Offer Letter'} Templates Found</span>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>

                <Pagination
                  className="mt-4"
                  current={currentPage}
                  total={emailTemplateState.count}
                  pageSize={pageSize}
                  hideOnSinglePage
                  onChange={(page) => setCurrentPage(page)}
                />
              </div>
            </div>
          )}

          {/* Offer Letter Templates Table */}
          {activeTab === 'offer' && (
            <div className="tab-content">
              <div className="tab-pane fade show active">
                <div className={`table-responsive offer-letter-list ${templates.length > 0 ? "" : "no-records"}`}>
                  <table className="table table-hover dataTable" style={{ width: "100%" }}>
                    <thead>
                      <tr role="row">
                        <th className="mw-50px">Sr. No</th>
                        <th className="mw-150px">Template Name</th>
                        {/* <th className="mw-200px">Template Content</th> */}
                        <th className="mw-80px">Status</th>
                        <th className="mw-120px">Date & Time</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {templates.length > 0 ? (
                        templates.map((template: any, index: number) => (
                          <tr key={template.id}>
                            <th>{(currentPage - 1) * pageSize + index + 1}</th>
                            <td>
                                {template.template_name}
                            </td>
                            {/* <td className="query-30-chars">
                              <div dangerouslySetInnerHTML={{ __html: template.email_body?.substring(0, 100) + '...' }} />
                            </td> */}
                            <td>
                              <span className={`badge ${template.status === 1 ? 'bg-success' : 'bg-secondary'}`}>
                                {template.status === 1 ? 'Active' : 'Inactive'}
                              </span>
                            </td>
                            <td>{new Date(template.created_at).toLocaleDateString()}</td>
                            <td>
                              <div className="template-action no-arrow">
                                <Dropdown>
                                  <Dropdown.Toggle
                                    id={`offer-template-dropdown-${template.id}`}
                                    className="text-decoration-none"
                                    as="span"
                                    type="button"
                                    role="button"
                                    aria-haspopup="true"
                                    aria-expanded="false">
                                    <span className="text-black small">
                                      <MoreVertIcon />
                                    </span>
                                  </Dropdown.Toggle>
                                  <Dropdown.Menu
                                    className="dropdown-menu-right shadow animated--grow-in"
                                    aria-labelledby={`offer-template-dropdown-${template.id}`}>
                                    <Dropdown.Item
                                      onClick={() => handleViewTemplate(template.id, 'offer')}
                                      className="no-decoration">
                                      View Template
                                    </Dropdown.Item>
                                    <Dropdown.Item
                                      onClick={() => handleEditTemplate(template.id, 'offer')}
                                      className="no-decoration">
                                      Edit Template
                                    </Dropdown.Item>
                                  </Dropdown.Menu>
                                </Dropdown>
                              </div>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr className="no-records">
                          <td colSpan={6} className="text-center">
                            <span>No Offer Letter Templates Found</span>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>

                <Pagination
                  className="mt-4"
                  current={currentPage}
                  total={emailTemplateState.count}
                  pageSize={pageSize}
                  hideOnSinglePage
                  onChange={(page) => setCurrentPage(page)}
                />
              </div>
            </div>
          )}
        </div>
      </PageHeader>
    </section>
  );
}

export const getServerSideProps: GetServerSideProps = async ({
  req,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    { wildcard: true },
    "template_management",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
      pageDetail: pageDetail,
    },
  };
};

TemplateManagementPage.layout = PrivateLayout;