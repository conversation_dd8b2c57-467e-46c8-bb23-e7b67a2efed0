import React, { useCallback, useEffect, useState } from 'react'
import { PrivateLayout } from "@src/components/Layout";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { GetServerSideProps } from "next";
import PageHeader from "@src/components/PageHeader/PageHeader";
import { KeyPairInterface } from "@src/redux/interfaces";
import { Button } from "react-bootstrap";
import { Pagination } from "antd";
import Dropdown from "react-bootstrap/Dropdown";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { useAppDispatch } from "@src/redux/store";
import { openDialog, setLoader, getAllEmailTemplateList, getAllOfferLetterTemplateList } from "@src/redux/actions";
import DialogComponents from "@src/components/DialogComponents";
import { useRouter } from "next/router";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";

type TemplateManagementPageProps = {
  pageDetail: KeyPairInterface;
  subdomain: string;
};

// Mock data for email templates
const emailTemplates = [
  {
    id: 1,
    name: "Welcome Email",
    subject: "Welcome to Our Company",
    type: "Welcome",
    status: "Active",
    created_at: "December 15, 2024 10:30 AM",
    created_by: "Admin User",
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Welcome to {{company_name}}, {{candidate_name}}!</h2>
        <p>We are excited to have you join our team. This email confirms that your application has been received and we look forward to working with you.</p>
        <p>If you have any questions, please don't hesitate to contact our HR team at {{hr_contact}}.</p>
        <p>Best regards,<br>The {{company_name}} Team</p>
      </div>
    `
  },
  {
    id: 2,
    name: "Interview Invitation",
    subject: "Interview Invitation - {{position}}",
    type: "Interview",
    status: "Active",
    created_at: "December 14, 2024 02:15 PM",
    created_by: "HR Manager",
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Interview Invitation</h2>
        <p>Dear {{candidate_name}},</p>
        <p>We are pleased to invite you for an interview for the position of <strong>{{position}}</strong> at {{company_name}}.</p>
        <p><strong>Interview Details:</strong></p>
        <ul>
          <li>Date & Time: {{interview_date}}</li>
          <li>Location: {{job_location}}</li>
          <li>Interviewer: {{interviewer_name}}</li>
        </ul>
        <p>Please confirm your attendance by replying to this email.</p>
        <p>Best regards,<br>{{interviewer_name}}</p>
      </div>
    `
  },
  {
    id: 3,
    name: "Rejection Email",
    subject: "Application Status Update",
    type: "Rejection",
    status: "Inactive",
    created_at: "December 13, 2024 09:45 AM",
    created_by: "Admin User",
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Application Status Update</h2>
        <p>Dear {{candidate_name}},</p>
        <p>Thank you for your interest in the {{position}} position at {{company_name}}.</p>
        <p>After careful consideration, we have decided to move forward with other candidates whose qualifications more closely match our current needs.</p>
        <p>We appreciate the time you invested in the application process and encourage you to apply for future opportunities that match your skills and experience.</p>
        <p>Best regards,<br>{{company_name}} HR Team</p>
      </div>
    `
  }
];

// Mock data for offer letter templates
const offerLetterTemplates = [
  {
    id: 1,
    name: "Software Engineer Offer",
    position: "Software Engineer",
    department: "Engineering",
    status: "Active",
    created_at: "December 15, 2024 11:00 AM",
    created_by: "HR Manager",
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #333;">{{company_name}}</h1>
          <h2 style="color: #666;">OFFER LETTER</h2>
        </div>

        <p>Dear {{candidate_name}},</p>

        <p>We are pleased to offer you the position of <strong>{{position}}</strong> in our {{department}} department at {{company_name}}.</p>

        <h3 style="color: #333;">Position Details:</h3>
        <ul>
          <li><strong>Position:</strong> {{position}}</li>
          <li><strong>Department:</strong> {{department}}</li>
          <li><strong>Reporting Manager:</strong> {{reporting_manager}}</li>
          <li><strong>Start Date:</strong> {{start_date}}</li>
        </ul>

        <h3 style="color: #333;">Compensation & Benefits:</h3>
        <ul>
          <li><strong>Annual Salary:</strong> {{salary}}</li>
          <li><strong>Benefits:</strong> {{benefits}}</li>
        </ul>

        <p>Please confirm your acceptance of this offer by signing and returning this letter by [Date].</p>

        <p>We look forward to welcoming you to our team!</p>

        <p>Sincerely,<br>
        {{company_name}} HR Team</p>

        <div style="margin-top: 50px;">
          <p>Candidate Signature: _________________________ Date: _________</p>
        </div>
      </div>
    `
  },
  {
    id: 2,
    name: "Marketing Manager Offer",
    position: "Marketing Manager",
    department: "Marketing",
    status: "Active",
    created_at: "December 12, 2024 03:30 PM",
    created_by: "Admin User",
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #333;">{{company_name}}</h1>
          <h2 style="color: #666;">EMPLOYMENT OFFER</h2>
        </div>

        <p>Dear {{candidate_name}},</p>

        <p>Congratulations! We are excited to offer you the position of <strong>{{position}}</strong> with {{company_name}}.</p>

        <h3 style="color: #333;">Role Information:</h3>
        <ul>
          <li><strong>Job Title:</strong> {{position}}</li>
          <li><strong>Department:</strong> {{department}}</li>
          <li><strong>Direct Report:</strong> {{reporting_manager}}</li>
          <li><strong>Expected Start Date:</strong> {{start_date}}</li>
        </ul>

        <h3 style="color: #333;">Compensation Package:</h3>
        <ul>
          <li><strong>Base Salary:</strong> {{salary}} per annum</li>
          <li><strong>Employee Benefits:</strong> {{benefits}}</li>
        </ul>

        <p>This offer is contingent upon successful completion of background checks and reference verification.</p>

        <p>Please indicate your acceptance by signing below and returning this letter within 5 business days.</p>

        <p>We are thrilled about the possibility of you joining our team!</p>

        <p>Best regards,<br>
        Human Resources Department<br>
        {{company_name}}</p>

        <div style="margin-top: 50px;">
          <p>I accept this offer: _________________________ Date: _________</p>
        </div>
      </div>
    `
  },
  {
    id: 3,
    name: "Sales Representative Offer",
    position: "Sales Representative",
    department: "Sales",
    status: "Inactive",
    created_at: "December 10, 2024 01:20 PM",
    created_by: "HR Manager",
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #333;">{{company_name}}</h1>
          <h2 style="color: #666;">JOB OFFER LETTER</h2>
        </div>

        <p>Dear {{candidate_name}},</p>

        <p>We are delighted to extend an offer for the position of <strong>{{position}}</strong> in our {{department}} team.</p>

        <h3 style="color: #333;">Employment Details:</h3>
        <ul>
          <li><strong>Position:</strong> {{position}}</li>
          <li><strong>Department:</strong> {{department}}</li>
          <li><strong>Supervisor:</strong> {{reporting_manager}}</li>
          <li><strong>Commencement Date:</strong> {{start_date}}</li>
        </ul>

        <h3 style="color: #333;">Remuneration:</h3>
        <ul>
          <li><strong>Annual Salary:</strong> {{salary}}</li>
          <li><strong>Commission Structure:</strong> As per company policy</li>
          <li><strong>Benefits Package:</strong> {{benefits}}</li>
        </ul>

        <p>Your employment will be subject to our standard terms and conditions of employment.</p>

        <p>Please confirm your acceptance by signing and returning this offer letter by [Date].</p>

        <p>We look forward to your positive response and to welcoming you to our sales team!</p>

        <p>Yours sincerely,<br>
        {{company_name}} Management</p>

        <div style="margin-top: 50px;">
          <p>Accepted by: _________________________ Date: _________</p>
        </div>
      </div>
    `
  }
];

export default function TemplateManagementPage({
  pageDetail,
  subdomain,
}: TemplateManagementPageProps) {
  const [activeTab, setActiveTab] = useState<'email' | 'offer'>('email');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const dispatch = useAppDispatch();
  const router = useRouter();

  // Get email templates from Redux store
  const emailTemplateState = useSelector((state: RootState) => state.emailTemplate);

  // Fetch the template data with pagination
  const fetchData = useCallback(
    async (page: number, limit: number) => {
      try {
        dispatch(setLoader(true));
        if (activeTab === 'email') {
          await dispatch(getAllEmailTemplateList({ page, limit }));
        }
        // TODO: Add offer letter template fetching when API is ready
        dispatch(setLoader(false));
      } catch (err) {
        console.error("Error fetching template data:", err);
        dispatch(setLoader(false));
      }
    },
    [dispatch, activeTab],
  );

  useEffect(() => {
    if (subdomain) {
      fetchData(currentPage, pageSize);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subdomain, activeTab, currentPage]);

  const handleEditTemplate = (templateId: number, type: 'email' | 'offer') => {
    router.push(`/admin/template_management/${templateId}?type=${type}`);
  };

  const handleViewTemplate = (templateId: number, type: 'email' | 'offer') => {
    if (type === 'email') {
      const template = emailTemplateState.rows.find(t => t.id === templateId);
      if (template) {
        dispatch(
          openDialog({
            config: DialogComponents.TEMPLATE_PREVIEW_MODAL,
            options: {
              template: {
                id: template.id,
                name: template.template_name,
                content: template.email_body,
                type: 'email',
                status: template.status === 1 ? 'Active' : 'Inactive',
                created_at: template.created_at,
                created_by: 'System' // TODO: Add created_by field to backend
              }
            },
          })
        );
      }
    } else {
      // TODO: Handle offer letter templates when API is ready
      const template = offerLetterTemplates.find(t => t.id === templateId);
      if (template) {
        dispatch(
          openDialog({
            config: DialogComponents.TEMPLATE_PREVIEW_MODAL,
            options: {
              template: {
                ...template,
                type: type
              }
            },
          })
        );
      }
    }
  };



  return (
    <section className="template">
      <PageHeader
        pageTitle={pageDetail?.title ?? "Template Management"}
        pageDescription={
          pageDetail?.description ?? "Manage email templates and offer letter templates"
        }
      >

        <div className="box-content">
          {/* Add New Template Button */}
          <div className="d-flex justify-content-end mb-3">
            <Button
              onClick={() => router.push('/admin/template_management/new')}
              className="btn btn-theme">
               {activeTab == 'email' ? '+ Add Email Template' : '+ Add Offer Letter Template' }
            </Button>
          </div>

          {/* Tab Navigation */}
          <div className="mb-4">
            <nav className="nav nav-tabs">
              <button
                className={`nav-link ${activeTab === 'email' ? 'active' : ''}`}
                onClick={() => setActiveTab('email')}
                type="button">
                Email Templates
              </button>
              <button
                className={`nav-link ${activeTab === 'offer' ? 'active' : ''}`}
                onClick={() => setActiveTab('offer')}
                type="button">
                Offer Letter Templates
              </button>
            </nav>
          </div>

          {/* Email Templates Table */}
          {activeTab === 'email' && (
            <div className="tab-content">
              <div className="tab-pane fade show active">
                <div className={`table-responsive email-template-list ${emailTemplateState.rows.length > 0 ? "" : "no-records"}`}>
                  <table className="table table-hover dataTable" style={{ width: "100%" }}>
                    <thead>
                      <tr role="row">
                        <th className="mw-50px">Sr. No</th>
                        <th className="mw-150px">Template Name</th>
                        <th className="mw-200px">Email Body</th>
                        <th className="mw-80px">Status</th>
                        <th className="mw-120px">Date & Time</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {emailTemplateState.rows.length > 0 ? (
                        emailTemplateState.rows.map((template, index) => (
                          <tr key={template.id}>
                            <th>{(currentPage - 1) * pageSize + index + 1}</th>
                            <td>
                                {template.template_name}
                            </td>
                            <td className="query-30-chars">
                              <div dangerouslySetInnerHTML={{ __html: template.email_body?.substring(0, 100) + '...' }} />
                            </td>
                            <td>
                              <span className={`badge ${template.status === 1 ? 'bg-success' : 'bg-secondary'}`}>
                                {template.status === 1 ? 'Active' : 'Inactive'}
                              </span>
                            </td>
                            <td>{new Date(template.created_at).toLocaleDateString()}</td>
                            <td>
                              <div className="template-action no-arrow">
                                <Dropdown>
                                  <Dropdown.Toggle
                                    id={`email-template-dropdown-${template.id}`}
                                    className="text-decoration-none"
                                    as="span"
                                    type="button"
                                    role="button"
                                    aria-haspopup="true"
                                    aria-expanded="false">
                                    <span className="text-black small">
                                      <MoreVertIcon />
                                    </span>
                                  </Dropdown.Toggle>
                                  <Dropdown.Menu
                                    className="dropdown-menu-right shadow animated--grow-in"
                                    aria-labelledby={`email-template-dropdown-${template.id}`}>
                                    <Dropdown.Item
                                      onClick={() => handleViewTemplate(template.id, 'email')}
                                      className="no-decoration">
                                      View Template
                                    </Dropdown.Item>
                                    <Dropdown.Item
                                      onClick={() => handleEditTemplate(template.id, 'email')}
                                      className="no-decoration">
                                      Edit Template
                                    </Dropdown.Item>
                                  </Dropdown.Menu>
                                </Dropdown>
                              </div>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr className="no-records">
                          <td colSpan={6} className="text-center">
                            <span>No Email Templates Found</span>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>

                <Pagination
                  className="mt-4"
                  current={currentPage}
                  total={emailTemplateState.count}
                  pageSize={pageSize}
                  hideOnSinglePage
                  onChange={(page) => setCurrentPage(page)}
                />
              </div>
            </div>
          )}

          {/* Offer Letter Templates Table */}
          {activeTab === 'offer' && (
            <div className="tab-content">
              <div className="tab-pane fade show active">
                <div className={`table-responsive offer-letter-list ${offerLetterTemplates.length > 0 ? "" : "no-records"}`}>
                  <table className="table table-hover dataTable" style={{ width: "100%" }}>
                    <thead>
                      <tr role="row">
                        <th className="mw-50px">Sr. No</th>
                        <th className="mw-150px">Template Name</th>
                        <th className="mw-150px">Position</th>
                        <th className="mw-120px">Department</th>
                        <th className="mw-80px">Status</th>
                        <th className="mw-120px">Created By</th>
                        <th className="mw-120px">Date & Time</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {offerLetterTemplates.length > 0 ? (
                        offerLetterTemplates.map((template, index) => (
                          <tr key={template.id}>
                            <th>{index + 1}</th>
                            <td>
                                {template.name}
                            </td>
                            <td>{template.position}</td>
                            <td>{template.department}</td>
                            <td>
                              <span className={`badge ${template.status === 'Active' ? 'bg-success' : 'bg-secondary'}`}>
                                {template.status}
                              </span>
                            </td>
                            <td>{template.created_by}</td>
                            <td>{template.created_at}</td>
                            <td>
                              <div className="template-action no-arrow">
                                <Dropdown>
                                  <Dropdown.Toggle
                                    id={`offer-template-dropdown-${template.id}`}
                                    className="text-decoration-none"
                                    as="span"
                                    type="button"
                                    role="button"
                                    aria-haspopup="true"
                                    aria-expanded="false">
                                    <span className="text-black small">
                                      <MoreVertIcon />
                                    </span>
                                  </Dropdown.Toggle>
                                  <Dropdown.Menu
                                    className="dropdown-menu-right shadow animated--grow-in"
                                    aria-labelledby={`offer-template-dropdown-${template.id}`}>
                                    <Dropdown.Item
                                      onClick={() => handleViewTemplate(template.id, 'offer')}
                                      className="no-decoration">
                                      View Template
                                    </Dropdown.Item>
                                    <Dropdown.Item
                                      onClick={() => handleEditTemplate(template.id, 'offer')}
                                      className="no-decoration">
                                      Edit Template
                                    </Dropdown.Item>
                                  </Dropdown.Menu>
                                </Dropdown>
                              </div>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr className="no-records">
                          <td colSpan={8} className="text-center">
                            <span>No Offer Letter Templates Found</span>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>

                <Pagination
                  className="mt-4"
                  current={1}
                  total={offerLetterTemplates.length}
                  pageSize={10}
                  hideOnSinglePage
                  onChange={(page) => console.log('Offer letter templates page:', page)}
                />
              </div>
            </div>
          )}
        </div>
      </PageHeader>
    </section>
  );
}

export const getServerSideProps: GetServerSideProps = async ({
  req,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    { wildcard: true },
    "template_management",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
      pageDetail: pageDetail,
    },
  };
};

TemplateManagementPage.layout = PrivateLayout;