import React, { useState } from 'react'
import { PrivateLayout } from "@src/components/Layout";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { GetServerSideProps } from "next";
import { KeyPairInterface } from "@src/redux/interfaces";
import { Button, Card, Form } from "react-bootstrap";
import { CustomEditor } from '@src/components/CustomEditor';
import { useAppDispatch } from "@src/redux/store";
import { openDialog, setLoader } from "@src/redux/actions";
import DialogComponents from "@src/components/DialogComponents";
import { useRouter } from "next/router";
import VisibilityIcon from "@mui/icons-material/Visibility";
import SaveIcon from "@mui/icons-material/Save";

import { emailTemplateApi } from "@src/apis/wildcardApis";
import flashMessage from "@src/components/FlashMessage";

type NewTemplatePageProps = {
  pageDetail: KeyPairInterface;
};

interface TemplateState {
  name: string;
  subject?: string;
  position?: string;
  department?: string;
  type: 'email' | 'offer';
  content: string;
}

export default function NewTemplatePage({
  pageDetail,
}: NewTemplatePageProps) {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { type } = router.query;

  // Initialize template data based on type
  const [templateData, setTemplateData] = useState<TemplateState>({
    name: "",
    subject: type === 'email' ? "" : undefined,
    position: type === 'offer' ? "" : undefined,
    department: type === 'offer' ? "" : undefined,
    type: (type as 'email' | 'offer') || 'email',
    content: type === 'email' ? 
      `<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Email Subject Here</h2>
        <p>Dear {{candidate_name}},</p>
        <p>Your email content goes here...</p>
        <p>Best regards,<br>{{company_name}} Team</p>
      </div>` :
      `<div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #333;">{{company_name}}</h1>
          <h2 style="color: #666;">OFFER LETTER</h2>
        </div>
        <p>Dear {{candidate_name}},</p>
        <p>We are pleased to offer you the position of <strong>{{position}}</strong>...</p>
        <p>Sincerely,<br>{{company_name}} HR Team</p>
      </div>`
  });

  const [errors, setErrors] = useState({ 
    name: "",
    subject: "",
    position: "",
    department: "",
    content: "" 
  });

  // Handle preview functionality
  const handlePreview = () => {
    const previewTemplate = {
      ...templateData,
      id: 0,
      status: "Draft",
      created_by: "Current User",
      created_at: new Date().toLocaleString()
    };

    dispatch(
      openDialog({
        config: DialogComponents.TEMPLATE_PREVIEW_MODAL,
        options: {
          template: previewTemplate,
          onEdit: () => {
            console.log("Already in edit mode");
          },
        },
      })
    );
  };

  // Handle save functionality
  const handleSave = async () => {
    // Validate required fields
    const newErrors = {
      name: templateData.name.trim() === "" ? "Template name is required" : "",
      subject: templateData.type === 'email' && templateData.subject?.trim() === "" ? "Subject is required for email templates" : "",
      position: templateData.type === 'offer' && templateData.position?.trim() === "" ? "Position is required for offer letter templates" : "",
      department: templateData.type === 'offer' && templateData.department?.trim() === "" ? "Department is required for offer letter templates" : "",
      content: templateData.content.trim() === "" ? "Template content is required" : ""
    };

    setErrors(newErrors);

    // Check if there are any errors
    const hasErrors = Object.values(newErrors).some(error => error !== "");

    if (!hasErrors) {
      try {
        dispatch(setLoader(true));

        // Prepare template name based on type
        let templateName = templateData.name.trim();
        if (templateData.type === 'offer') {
          // Add "Candidate Offer Letter" prefix for offer letter templates
          templateName = `Candidate Offer Letter - ${templateName}`;
        }

        // Prepare the API payload
        const payload = {
          template_name: templateName,
          email_body: templateData.content
        };

        const { success, ...response } = await emailTemplateApi.createEmailTemplate(payload);

        if (success) {
          flashMessage("Template created successfully!", "success");
          router.push('/admin/template_management');
        } else {
          flashMessage(response.message || "Failed to create template", "error");
        }
      } catch (error) {
        console.error("Error creating template:", error);
        flashMessage("An error occurred while creating the template", "error");
      } finally {
        dispatch(setLoader(false));
      }
    }
  };

  // Handle input changes
  const handleInputChange = (field: keyof TemplateState, value: string) => {
    setTemplateData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error for this field when user starts typing
    if (errors[field as keyof typeof errors]) {
      setErrors(prev => ({
        ...prev,
        [field]: ""
      }));
    }
  };

  return (
    <section className="">
      <div className="d-flex align-items-center flex-wrap flex-lg-nowrap gap-2 justify-content-between mb-3">
        <div className="bredcrubs d-flex gap-3 align-items-end">
          <h1 className="m-0 page-head">
            {pageDetail?.title ?? "Template Management"}
          </h1>
          <h4 className="m-0 page-head primary-clr position-relative ps-3">
            New {templateData.type === 'email' ? 'Email Template' : 'Offer Letter Template'}
          </h4>
        </div>
      </div>

      <div className="box-content">
        <div className="row">
          <div className="col-lg-12">
            <Card>
              <Card.Body>
                {/* Template Type Selection */}
                <div className="mb-4">
                  <Form.Label className="fw-bold mb-3">Template Type</Form.Label>
                  <div className="d-flex gap-3">
                    <Form.Check
                      type="radio"
                      id="email-type"
                      label="Email Template"
                      checked={templateData.type === 'email'}
                      onChange={() => {
                        setTemplateData(prev => ({
                          ...prev,
                          type: 'email',
                          subject: "",
                          position: undefined,
                          department: undefined
                        }));
                      }}
                    />
                    <Form.Check
                      type="radio"
                      id="offer-type"
                      label="Offer Letter Template"
                      checked={templateData.type === 'offer'}
                      onChange={() => {
                        setTemplateData(prev => ({
                          ...prev,
                          type: 'offer',
                          subject: undefined,
                          position: "",
                          department: ""
                        }));
                      }}
                    />
                  </div>
                </div>

                {/* Template Basic Information */}
                <div className="mb-4">
                  <h5 className="fw-bold mb-3">Template Details</h5>
                  
                  <div className="row">
                    <div className="col-md-6">
                      <Form.Group className="mb-3">
                        <Form.Label>Template Name <span className="text-danger">*</span></Form.Label>
                        <Form.Control
                          type="text"
                          value={templateData.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          placeholder="Enter template name"
                          isInvalid={!!errors.name}
                        />
                        <Form.Control.Feedback type="invalid">
                          {errors.name}
                        </Form.Control.Feedback>
                      </Form.Group>
                    </div>
                    
                    {templateData.type === 'email' && (
                      <div className="col-md-6">
                        <Form.Group className="mb-3">
                          <Form.Label>Email Subject <span className="text-danger">*</span></Form.Label>
                          <Form.Control
                            type="text"
                            value={templateData.subject || ''}
                            onChange={(e) => handleInputChange('subject', e.target.value)}
                            placeholder="Enter email subject"
                            isInvalid={!!errors.subject}
                          />
                          <Form.Control.Feedback type="invalid">
                            {errors.subject}
                          </Form.Control.Feedback>
                        </Form.Group>
                      </div>
                    )}
                    
                    {templateData.type === 'offer' && (
                      <>
                        <div className="col-md-3">
                          <Form.Group className="mb-3">
                            <Form.Label>Position <span className="text-danger">*</span></Form.Label>
                            <Form.Control
                              type="text"
                              value={templateData.position || ''}
                              onChange={(e) => handleInputChange('position', e.target.value)}
                              placeholder="Enter position"
                              isInvalid={!!errors.position}
                            />
                            <Form.Control.Feedback type="invalid">
                              {errors.position}
                            </Form.Control.Feedback>
                          </Form.Group>
                        </div>
                        <div className="col-md-3">
                          <Form.Group className="mb-3">
                            <Form.Label>Department <span className="text-danger">*</span></Form.Label>
                            <Form.Control
                              type="text"
                              value={templateData.department || ''}
                              onChange={(e) => handleInputChange('department', e.target.value)}
                              placeholder="Enter department"
                              isInvalid={!!errors.department}
                            />
                            <Form.Control.Feedback type="invalid">
                              {errors.department}
                            </Form.Control.Feedback>
                          </Form.Group>
                        </div>
                      </>
                    )}
                  </div>
                </div>

                {/* Template Content Editor */}
                <div className="mb-4">
                  <Form.Label className="fw-bold mb-3">
                    Template Content <span className="text-danger">*</span>
                  </Form.Label>
                  <div className="position-relative">
                    <CustomEditor
                      value={templateData.content}
                      onChange={(content) => handleInputChange('content', content)}
                    />
                    {errors.content && (
                      <div className="text-danger mt-2">{errors.content}</div>
                    )}
                  </div>
                </div>

                {/* Available Variables Help */}
                <div className="alert alert-info">
                  <h6 className="mb-2">Available Variables:</h6>
                  <div className="row">
                    {templateData.type === 'email' ? (
                      <>
                        <div className="col-md-6">
                          <small>
                            <code>{'{{candidate_name}}'}</code> - Candidate's name<br/>
                            <code>{'{{position}}'}</code> - Job position<br/>
                            <code>{'{{company_name}}'}</code> - Company name<br/>
                            <code>{'{{interview_date}}'}</code> - Interview date
                          </small>
                        </div>
                        <div className="col-md-6">
                          <small>
                            <code>{'{{interviewer_name}}'}</code> - Interviewer's name<br/>
                            <code>{'{{job_location}}'}</code> - Job location<br/>
                            <code>{'{{hr_contact}}'}</code> - HR contact info
                          </small>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="col-md-6">
                          <small>
                            <code>{'{{candidate_name}}'}</code> - Candidate's name<br/>
                            <code>{'{{position}}'}</code> - Job position<br/>
                            <code>{'{{salary}}'}</code> - Salary amount<br/>
                            <code>{'{{start_date}}'}</code> - Start date
                          </small>
                        </div>
                        <div className="col-md-6">
                          <small>
                            <code>{'{{company_name}}'}</code> - Company name<br/>
                            <code>{'{{department}}'}</code> - Department<br/>
                            <code>{'{{benefits}}'}</code> - Benefits package<br/>
                            <code>{'{{reporting_manager}}'}</code> - Manager name
                          </small>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </Card.Body>
              
              <Card.Footer>
                <div className="d-flex justify-content-end align-items-center gap-2">
                  <Button 
                    variant="outline-primary"
                    onClick={handlePreview}
                    className="d-flex align-items-center gap-2">
                    <VisibilityIcon fontSize="small" />
                    Preview
                  </Button>
                  <Button 
                    variant="primary"
                    onClick={handleSave}
                    className="d-flex align-items-center gap-2">
                    <SaveIcon fontSize="small" />
                    Create Template
                  </Button>
                </div>
              </Card.Footer>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}

export const getServerSideProps: GetServerSideProps = async ({
  req,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    { wildcard: true },
    "template_management",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
      pageDetail: pageDetail,
    },
  };
};

NewTemplatePage.layout = PrivateLayout;