import React, { useState } from 'react';
import { Button } from "antd";
import PrintIcon from "@mui/icons-material/Print";
import EditIcon from "@mui/icons-material/Edit";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";

interface TemplateData {
  id: number;
  name: string;
  type: 'email' | 'offer';
  subject?: string;
  content: string;
  position?: string;
  department?: string;
  status: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
}

interface TemplatePreviewModalProps {
  close: () => void;
  template: TemplateData;
}

const TemplatePreviewModal: React.FC<TemplatePreviewModalProps> = ({
  close,
  template
}) => {
  

  return (
    <div className="template-preview-modal">
      {/* Template Header */}
      <div className="template-preview-header mb-4">
        <div className="row">
          <div className="col-md-8">
            <h4 className="mb-2">{template.name}</h4>
            <div className="template-meta">
              {template.subject && (
                <p className="mb-1"><strong>Subject:</strong> {template.subject}</p>
              )}
              {template.position && (
                <p className="mb-1"><strong>Position:</strong> {template.position}</p>
              )}
              {template.department && (
                <p className="mb-1"><strong>Department:</strong> {template.department}</p>
              )}
              <p className="mb-1">
                <strong>Type:</strong> {template.type === 'email' ? 'Email Template' : 'Offer Letter Template'}
              </p>
              <p className="mb-1">
                <strong>Status:</strong>
                <span className={`badge ms-2 ${template.status === 'Active' ? 'bg-success' : 'bg-secondary'}`}>
                  {template.status}
                </span>
              </p>
              <p className="mb-1"><strong>Created by:</strong> {template.created_by}</p>
              <p className="mb-0"><strong>Updated on:</strong> {template.updated_at  || ""}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Template Content */}
      <div className="template-content-preview">
        <div className="content-wrapper p-4 border rounded bg-white">
          <div
            className="template-content"
            dangerouslySetInnerHTML={{ __html: template.content }}
          />
        </div>
      </div>

      {/* Modal Footer */}
      <div className="ant-modal-footer mt-4">
        <div className="d-flex justify-content-between">
          <div>
            <Button onClick={close} className='btn btn-primary'>
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TemplatePreviewModal;