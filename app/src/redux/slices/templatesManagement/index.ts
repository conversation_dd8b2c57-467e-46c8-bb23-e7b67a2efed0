// slices/templateSlice.js

import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { AppDispatch } from "@src/redux/store";
import { templateManagement } from "@src/apis/wildcardApis";

interface TemplateState {
  rows: any[];
  limit: number;
  count: number;
  currentPage: number;
  template: null | any;
}

const initialState: TemplateState = {
  rows: [],
  limit: 10,
  count: 0,
  currentPage: 1,
  template: null
};

const templateSlice = createSlice({
  name: "templates",
  initialState,
  reducers: {
    setTemplatesData: (state, action) => {
      console.log(state, ":::state", action);
      state = { ...state, ...action.payload };
      
      return state;
    },
    updateTemaplateDetail: (state, action) => {
      state.rows = UpdateDetailInRow(state.rows, action.payload);
    },
    setTemplateState: (
      state,
      action: PayloadAction<null | any>,
    ) => {
      state.template = action.payload;
    },
  },
});

const { setTemplatesData, setTemplateState } =
  templateSlice.actions;

export const { updateTemaplateDetail } = templateSlice.actions;

const UpdateDetailInRow = (
  templateData: any[],
  payload: any,
) => {
  const detail = payload.data;
  const newDepartmentData = templateData.map(
    (template: any) => {
      if (template.id === payload.id) {
        return { ...template, ...detail };
      }
      return template;
    },
  );
  return newDepartmentData;
};

export const getAllTemplateList =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await templateManagement.getAllTemplates(params);

    if (success) {
      const { page, rows, count, limit } = response.data;
      dispatch(
        setTemplatesData({
          rows: rows,
          limit: limit,
          count: count,
          currentPage: page,
        }),
      );
    } else {
      dispatch(
        setTemplatesData({
          rows: [],
          limit: 10,
          count: 0,
          currentPage: 1,
        }),
      );
    }
  };


export const getTemplateDetail =
  (id: number, callback?: (success: boolean, response: any) => void) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await templateManagement.getTemplateDetail(id);
    if (success) {
      dispatch(setTemplateState(response.data));
    } else {
      dispatch(setTemplateState(null));
    }
    callback && callback(success, response);
  };


export default templateSlice.reducer;
