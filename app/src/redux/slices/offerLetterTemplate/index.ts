// slices/offerLetterTemplateSlice.js

import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { OfferLetterTemplateInterface } from "@src/redux/interfaces";
import { AppDispatch } from "@src/redux/store";
import offerLetterTemplateApi from "@src/apis/wildcardApis/offerLetterTemplateApi";

interface OfferLetterTemplateState {
  rows: OfferLetterTemplateInterface[]; // Adjust the type according to your offer letter template data structure
  limit: number;
  count: number;
  currentPage: number;
  offerLetterTemplate: null | OfferLetterTemplateInterface;
  options: Array<{ value: string | number; label: string }>;
}

const initialState: OfferLetterTemplateState = {
  rows: [],
  limit: 10,
  count: 0,
  currentPage: 1,
  offerLetterTemplate: null,
  options: [],
};

const offerLetterTemplateSlice = createSlice({
  name: "offerLetterTemplate",
  // `createSlice` will infer the state type from the `initialState` argument
  initialState,
  reducers: {
    setOfferLetterTemplateData: (state, action) => {
      state = { ...state, ...action.payload };
      return state;
    },
    updateOfferLetterTemplateDetail: (state, action) => {
      state.rows = UpdateDetailInRow(state.rows, action.payload);
    },
    setOfferLetterTemplateState: (
      state,
      action: PayloadAction<null | OfferLetterTemplateInterface>,
    ) => {
      state.offerLetterTemplate = action.payload;
    },
    setOfferLetterTemplateOptionsState: (
      state,
      action: PayloadAction<Array<{ value: string | number; label: string }>>,
    ) => {
      state.options = action.payload;
    },
  },
});

const {
  setOfferLetterTemplateData,
  setOfferLetterTemplateState,
  setOfferLetterTemplateOptionsState,
} = offerLetterTemplateSlice.actions;

export const { updateOfferLetterTemplateDetail } = offerLetterTemplateSlice.actions;

const UpdateDetailInRow = (
  offerLetterTemplateData: OfferLetterTemplateInterface[],
  payload: any,
) => {
  const detail = payload.data;
  const newOfferLetterTemplateData = offerLetterTemplateData.map(
    (offerLetterTemplate: OfferLetterTemplateInterface) => {
      if (offerLetterTemplate.id === payload.id) {
        return { ...offerLetterTemplate, ...detail };
      }
      return offerLetterTemplate;
    },
  );
  return newOfferLetterTemplateData;
};

/**
 * Retrieves all offer letter template list from the server and dispatches an action to update the offer letter template data in the Redux store.
 * @param params Parameters for fetching offer letter template list
 */
export const getAllOfferLetterTemplateList =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await offerLetterTemplateApi.getOfferLetterTemplateList(params);
    if (success) {
      const { page, rows, count, limit } = response.data;
      await dispatch(
        setOfferLetterTemplateData({
          rows: rows,
          limit: limit,
          count: count,
          currentPage: page,
        }),
      );
    } else {
      await dispatch(
        setOfferLetterTemplateData({
          rows: [],
          limit: 10,
          count: 0,
          currentPage: 1,
        }),
      );
    }
  };

/**
 * Retrieves specific offer letter template from the server and dispatches an action to update the offer letter template data in the Redux store.
 * @param id Offer letter template ID
 */
export const getOfferLetterTemplateDetail =
  (id: number) => async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await offerLetterTemplateApi.getOfferLetterTemplateDetail(id);
    if (success) {
      await dispatch(setOfferLetterTemplateState(response.data));
    } else {
      await dispatch(setOfferLetterTemplateState(null));
    }
  };

export default offerLetterTemplateSlice.reducer;
