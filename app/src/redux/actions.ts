export { setLoader, setLoaderText } from "./slices/loader";
export {
  loginAdmin,
  logoutAdmin,
  adminVerify,
  setAdminDetailState,
  AdminResetPassword,
  AdminVerifyUserAccount,
  loginEmployee,
  logoutEmployee,
  employeeVerify,
  setEmployeeDetailState,
  EmployeeResetPassword,
  clearAuthState,
  loginCandidate,
  logoutCandidate,
  candidateVerify,
  setCandidateDetailState,
  CandidateResetPassword,
} from "./slices/auth";

export {
  getAllAdminBusinessList,
  updateBusinessDetail,
} from "./slices/business";

export {
  getAllDepartmentList,
  getDepartmentDetail,
  updateDepartmentDetail,
  getAllDepartmentOptions,
} from "./slices/department";

export {
  getAllLocationList,
  getLocationDetail,
  updateLocationDetail,
  getAllLocationOptions,
} from "./slices/location";

export { setSideBar, toggleSideBar } from "./slices/menu";

export {
  openDialog,
  closeDialog,
  updateDialogProperties,
} from "./slices/dialog";

export {
  getAllWildcardEmployeeList,
  updateEmployeeDetail,
  getAllEmployeeOptions,
  getAllEmployeeRolesOptions,
} from "./slices/employee";

export {
  createOpportunity,
  getAllOpportunityList,
  getOpportunityDetail,
  updateOpportunity,
  updateOpportunityStatus,
  getAllOpportunityOptions,
} from "./slices/opportunity";

export {
  createInterview,
  getAllInterviewList,
  getInterviewDetail,
  updateInterview,
  updateInterviewStatus,
  getInterviewHistories,
} from "./slices/interview";

export {
  getAllCandidateInterviewList,
  getCandidateInterviewDetail,
  updateCandidateInterviewStatus,
  updateCandidateInterviewRecordState,
} from "./slices/candidateInterview";

export {
  getAllCandidateList,
  getCandidateDetail,
  updateCandidateStatus,
  getAllCandidateStatusOptions,
  getAllCandidateQualificationOptions,
  setCandidateState,
} from "./slices/candidate";

export { getAllPermissionList, getAllNodesList } from "./slices/setting";

export {
  getAllEmailTemplateList,
  getEmailTemplateDetail,
  updateEmailTemplateDetail,
  getAllEmailTemplateOptions,
} from "./slices/emailTemplate";

export {
  getAllOfferLetterTemplateList,
  getOfferLetterTemplateDetail,
  updateOfferLetterTemplateDetail,
} from "./slices/offerLetterTemplate";

export { getCandidateSelfDetail } from "./slices/wildcardCandidate";

export {
  getAllJobRequestList,
  getJobRequestDetail,
  updateJobRequestDetail,
} from "./slices/jobRequest";

export {
  getAllEmployeeSignatureList,
  updateEmployeeSignatureDetail,
  getEmployeeSignatureOptions,
} from "./slices/signature";


export {
  getTemplateDetail,
} from "./slices/templatesManagement";

export { getAllUserList, updateUserStatus } from "./slices/user";

export { getDashboardStats } from "./slices/dashboard";

export { setGoogleAuthToken, clearOauthState } from "./slices/ouath";
