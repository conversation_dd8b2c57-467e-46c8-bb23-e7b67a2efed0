/*colors*/
:root {
    --primary: #5e72e4;
    --primary-hover: #5e72e4;
    --secondary: #060b24;
    --secondary-dark: #0b1541;
    --text: #262626;
    --heading: #000102;
    --lightbg: #f2f7f9;
}

section.candidate-layout {
    height: 100vh;
}

section.candidate-layout .dashbaord {
    padding: 20px 20px;
}

/* ----------------------------------------------- navbar ---------------------------------------------- */
section.candidate-layout .sidebar {
    position: sticky !important;
    top: 0 !important;
    height: 100vh !important;
    overflow-y: auto !important; /* Enables vertical auto-scrolling */
    overflow-x: hidden !important; /* Disables horizontal scrolling */
    width: 7.5rem;
}

section.candidate-layout .sidebar.sidebar-dark {
    width: 8.5rem !important;
}

section.candidate-layout .sidebar .nav-item {
    position: relative;
}

section.candidate-layout .sidebar .nav-item:last-child {
    margin-bottom: 1rem;
}

section.candidate-layout .sidebar .nav-item .nav-link {
    text-align: center;
    /* padding: 0.75rem 1rem; */
    width: 7.5rem !important;
}

section.candidate-layout .sidebar .nav-item .nav-link span {
    font-size: 0.5rem;
    display: block;
}

section.candidate-layout .sidebar .nav-item.active .nav-link {
    font-weight: 700;
}

section.candidate-layout .sidebar .nav-item .collapse {
    position: absolute;
    left: calc(6.5rem + 1.5rem / 2);
    z-index: 1;
    top: 2px;
}

section.candidate-layout .sidebar .nav-item .collapse .collapse-inner {
    border-radius: 0.35rem;
    -webkit-box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

section.candidate-layout .sidebar .nav-item .collapsing {
    display: none;
    -webkit-transition: none;
    transition: none;
}

section.candidate-layout .sidebar .nav-item .collapse .collapse-inner,
section.candidate-layout .sidebar .nav-item .collapsing .collapse-inner {
    padding: 0.5rem 0;
    min-width: 10rem;
    font-size: 0.85rem;
    margin: 0 0 1rem 0;
}

section.candidate-layout
    .sidebar
    .nav-item
    .collapse
    .collapse-inner
    .collapse-header,
section.candidate-layout
    .sidebar
    .nav-item
    .collapsing
    .collapse-inner
    .collapse-header {
    margin: 0;
    white-space: nowrap;
    padding: 0.5rem 1.5rem;
    text-transform: uppercase;
    font-weight: 800;
    font-size: 0.65rem;
    color: #b7b9cc;
}

section.candidate-layout
    .sidebar
    .nav-item
    .collapse
    .collapse-inner
    .collapse-item,
section.candidate-layout
    .sidebar
    .nav-item
    .collapsing
    .collapse-inner
    .collapse-item {
    padding: 0.5rem 1rem;
    margin: 0 0.5rem;
    display: block;
    color: #3a3b45;
    text-decoration: none;
    border-radius: 0.35rem;
    white-space: nowrap;
}

section.candidate-layout
    .sidebar
    .nav-item
    .collapse
    .collapse-inner
    .collapse-item:hover,
section.candidate-layout
    .sidebar
    .nav-item
    .collapsing
    .collapse-inner
    .collapse-item:hover {
    background-color: #eaecf4;
}

section.candidate-layout
    .sidebar
    .nav-item
    .collapse
    .collapse-inner
    .collapse-item:active,
section.candidate-layout
    .sidebar
    .nav-item
    .collapsing
    .collapse-inner
    .collapse-item:active {
    background-color: #dddfeb;
}

section.candidate-layout
    .sidebar
    .nav-item
    .collapse
    .collapse-inner
    .collapse-item.active,
section.candidate-layout
    .sidebar
    .nav-item
    .collapsing
    .collapse-inner
    .collapse-item.active {
    color: #4e73df;
    font-weight: 700;
}

section.candidate-layout .sidebar #sidebarToggle {
    width: 2.5rem;
    height: 2.5rem;
    text-align: center;
    margin-bottom: 1rem;
    cursor: pointer;
}

section.candidate-layout .sidebar #sidebarToggle::after {
    font-weight: 900;
    content: "\f104";
    font-family: "Font Awesome 5 Free";
    margin-right: 0.1rem;
}

section.candidate-layout .sidebar #sidebarToggle:hover {
    text-decoration: none;
}

section.candidate-layout .sidebar #sidebarToggle:focus {
    outline: none;
}

section.candidate-layout .sidebar.toggled {
    width: 0 !important;
    overflow: hidden;
}

section.candidate-layout .sidebar.toggled #sidebarToggle::after {
    content: "\f105";
    font-family: "Font Awesome 5 Free";
    margin-left: 0.25rem;
}

section.candidate-layout .sidebar .sidebar-brand {
    height: 4.375rem;
    text-decoration: none;
    font-size: 1rem;
    font-weight: 800;
    padding: 1rem;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
    position: sticky;
    top: 0;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
    /* background-color: #ffffff; */
}

section.candidate-layout .sidebar .sidebar-brand .sidebar-brand-icon i {
    font-size: 2rem;
}

section.candidate-layout .sidebar .sidebar-brand .sidebar-brand-text {
    display: none;
}

section.candidate-layout .sidebar hr.sidebar-divider {
    margin: 0 1rem 1rem;
}

section.candidate-layout .sidebar .sidebar-heading {
    text-align: center;
    padding: 0 1rem;
    font-weight: 800;
    font-size: 0.65rem;
}

@media (min-width: 768px) {
    section.candidate-layout .sidebar {
        width: 14rem !important;
    }

    section.candidate-layout .sidebar.sidebar-dark {
        width: 17rem !important;
    }

    section.candidate-layout .sidebar .nav-item .collapse {
        position: relative;
        left: 0;
        z-index: 1;
        top: 0;
        -webkit-animation: none;
        animation: none;
    }

    section.candidate-layout .sidebar .nav-item .collapse .collapse-inner {
        border-radius: 0;
        -webkit-box-shadow: none;
        box-shadow: none;
    }

    section.candidate-layout .sidebar .nav-item .collapsing {
        display: block;
        -webkit-transition: height 0.15s ease;
        transition: height 0.15s ease;
    }

    section.candidate-layout .sidebar .nav-item .collapse,
    section.candidate-layout .sidebar .nav-item .collapsing {
        margin: 0 1rem;
    }

    section.candidate-layout .sidebar .nav-item .nav-link {
        display: block;
        width: 100%;
        text-align: left;
        padding: 1rem;
        width: 14rem !important;
    }

    section.candidate-layout .sidebar .nav-item .nav-link i {
        font-size: 0.75rem;
        margin-right: 0.25rem;
    }

    section.candidate-layout .sidebar .nav-item .nav-link span {
        font-size: 0.75rem;
        display: inline;
    }

    section.candidate-layout
        .sidebar
        .nav-item
        .nav-link[data-toggle="collapse"]::after {
        width: 1rem;
        text-align: center;
        float: right;
        vertical-align: 0;
        border: 0;
        font-weight: 900;
        content: "\f107";
        font-family: "Font Awesome 5 Free";
    }

    section.candidate-layout
        .sidebar
        .nav-item
        .nav-link[data-toggle="collapse"].collapsed::after {
        content: "\f105";
    }

    section.candidate-layout .sidebar .sidebar-brand .sidebar-brand-icon i {
        font-size: 2rem;
    }

    section.candidate-layout .sidebar .sidebar-brand .sidebar-brand-text {
        display: inline;
    }

    section.candidate-layout .sidebar .sidebar-heading {
        text-align: left;
    }

    section.candidate-layout .sidebar.toggled {
        overflow: visible;
        width: 6.5rem !important;
    }

    section.candidate-layout .sidebar.toggled .nav-item .collapse {
        position: absolute;
        left: calc(6.5rem + 1.5rem / 2);
        z-index: 1;
        top: 2px;
        -webkit-animation-name: growIn;
        animation-name: growIn;
        -webkit-animation-duration: 200ms;
        animation-duration: 200ms;
        -webkit-animation-timing-function:
            transform cubic-bezier(0.18, 1.25, 0.4, 1),
            opacity cubic-bezier(0, 1, 0.4, 1);
        animation-timing-function:
            transform cubic-bezier(0.18, 1.25, 0.4, 1),
            opacity cubic-bezier(0, 1, 0.4, 1);
    }

    section.candidate-layout
        .sidebar.toggled
        .nav-item
        .collapse
        .collapse-inner {
        -webkit-box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        border-radius: 0.35rem;
    }

    section.candidate-layout .sidebar.toggled .nav-item .collapsing {
        display: none;
        -webkit-transition: none;
        transition: none;
    }

    section.candidate-layout .sidebar.toggled .nav-item .collapse,
    section.candidate-layout .sidebar.toggled .nav-item .collapsing {
        margin: 0;
    }

    section.candidate-layout .sidebar.toggled .nav-item:last-child {
        margin-bottom: 1rem;
    }

    section.candidate-layout .sidebar.toggled .nav-item .nav-link {
        text-align: center;
        /* padding: 0.75rem 1rem; */
        width: 6.5rem !important;
    }

    section.candidate-layout .sidebar.toggled .nav-item .nav-link span {
        font-size: 0.65rem;
        display: block;
    }

    section.candidate-layout .sidebar.toggled .nav-item .nav-link i {
        margin-right: 0;
    }

    section.candidate-layout
        .sidebar.toggled
        .nav-item
        .nav-link[data-toggle="collapse"]::after {
        display: none;
    }

    section.candidate-layout
        .sidebar.toggled
        .sidebar-brand
        .sidebar-brand-icon
        i {
        font-size: 2rem;
    }

    section.candidate-layout
        .sidebar.toggled
        .sidebar-brand
        .sidebar-brand-text {
        display: none;
    }

    section.candidate-layout .sidebar.toggled .sidebar-heading {
        text-align: center;
    }
}

section.candidate-layout .sidebar-light .sidebar-brand {
    color: #6e707e;
}

section.candidate-layout .sidebar-light hr.sidebar-divider {
    border-top: 1px solid #eaecf4;
}

section.candidate-layout .sidebar-light .sidebar-heading {
    color: #b7b9cc;
}

section.candidate-layout .sidebar-light .nav-item .nav-link {
    color: #858796;
}

section.candidate-layout .sidebar-light .nav-item .nav-link i {
    color: #d1d3e2;
}

section.candidate-layout .sidebar-light .nav-item .nav-link:active,
section.candidate-layout .sidebar-light .nav-item .nav-link:focus,
section.candidate-layout .sidebar-light .nav-item .nav-link:hover {
    color: #6e707e;
}

section.candidate-layout .sidebar-light .nav-item .nav-link:active i,
section.candidate-layout .sidebar-light .nav-item .nav-link:focus i,
section.candidate-layout .sidebar-light .nav-item .nav-link:hover i {
    color: #6e707e;
}

section.candidate-layout
    .sidebar-light
    .nav-item
    .nav-link[data-toggle="collapse"]::after {
    color: #b7b9cc;
}

section.candidate-layout .sidebar-light .nav-item.active .nav-link {
    color: #6e707e;
}

section.candidate-layout .sidebar-light .nav-item.active .nav-link i {
    color: #6e707e;
}

section.candidate-layout .sidebar-light #sidebarToggle {
    background-color: #eaecf4;
}

section.candidate-layout .sidebar-light #sidebarToggle::after {
    color: #b7b9cc;
}

section.candidate-layout .sidebar-light #sidebarToggle:hover {
    background-color: #dddfeb;
}

section.candidate-layout .sidebar-dark .sidebar-brand {
    color: #fff;
}

section.candidate-layout .sidebar-dark hr.sidebar-divider {
    border-top: 1px solid rgba(1, 1, 1, 1) !important;
}

section.candidate-layout .sidebar-dark .sidebar-heading {
    color: rgba(255, 255, 255, 0.4);
}

section.candidate-layout .sidebar-dark .nav-item .nav-link {
    color: rgba(255, 255, 255, 0.8);
}

section.candidate-layout .sidebar-dark .nav-item .nav-link i {
    color: rgba(255, 255, 255, 0.3);
}

section.candidate-layout .sidebar-dark .nav-item .nav-link:active,
section.candidate-layout .sidebar-dark .nav-item .nav-link:hover {
    color: rgba(255, 255, 255, 1) !important;
}

section.candidate-layout .sidebar-dark .nav-item .nav-link {
    color: rgba(1, 1, 1, 1) !important;
}

section.candidate-layout .sidebar-dark .nav-item .nav-link:active i,
section.candidate-layout .sidebar-dark .nav-item .nav-link:hover i {
    color: rgba(255, 255, 255, 1) !important;
}

section.candidate-layout
    .sidebar-dark
    .nav-item
    .nav-link[data-toggle="collapse"]::after {
    color: rgba(255, 255, 255, 0.5);
}

section.candidate-layout .sidebar-dark .nav-item.active .nav-link {
    color: #fff !important;
}

section.candidate-layout .sidebar-dark .nav-item.active .nav-link i {
    color: #fff !important;
}

section.candidate-layout .sidebar-dark .nav-item {
    margin: 5px 10px;
    border-radius: 10px;
    border: 1px solid #111;
}

section.candidate-layout .sidebar-dark .nav-item.active {
    background: var(--primary) !important;
}

section.candidate-layout .sidebar-dark .nav-item:hover {
    background: var(--primary-hover);
}

section.candidate-layout .sidebar-dark #sidebarToggle {
    background-color: rgba(255, 255, 255, 0.2);
}

section.candidate-layout .sidebar-dark #sidebarToggle::after {
    color: rgba(255, 255, 255, 0.5);
}

section.candidate-layout .sidebar-dark #sidebarToggle:hover {
    background-color: rgba(255, 255, 255, 0.25);
}

section.candidate-layout .sidebar-dark.toggled #sidebarToggle::after {
    color: rgba(255, 255, 255, 0.5);
}

section.candidate-layout #wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

section.candidate-layout #wrapper #content-wrapper {
    background-color: #f8f9fc;
    width: 100%;
    overflow-x: hidden;
}

section.candidate-layout #wrapper #content-wrapper #content {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 auto;
    flex: 1 0 auto;
}

section.candidate-layout .container,
section.candidate-layout .container-fluid {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

section.candidate-layout .scroll-to-top {
    position: fixed;
    right: 1rem;
    bottom: 1rem;
    display: none;
    width: 2.75rem;
    height: 2.75rem;
    text-align: center;
    color: #fff;
    background: rgba(90, 92, 105, 0.5);
    line-height: 46px;
}

section.candidate-layout .scroll-to-top:focus,
section.candidate-layout .scroll-to-top:hover {
    color: white;
}

section.candidate-layout .scroll-to-top:hover {
    background: #5a5c69;
}

section.candidate-layout .scroll-to-top i {
    font-weight: 800;
}

section.candidate-layout .accordion > .card {
    overflow: hidden;
}

section.candidate-layout
    .accordion
    > .card:not(:first-of-type)
    .card-header:first-child {
    border-radius: 0;
}

section.candidate-layout
    .accordion
    > .card:not(:first-of-type):not(:last-of-type) {
    border-bottom: 0;
    border-radius: 0;
}

section.candidate-layout .accordion > .card:first-of-type {
    border-bottom: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}

section.candidate-layout .accordion > .card:last-of-type {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

section.candidate-layout .accordion > .card .card-header {
    margin-bottom: -1px;
}
/* ----------------------------------------------- navbar ---------------------------------------------- */

/* ----------------------------------------------- navbar ---------------------------------------------- */
section.candidate-layout .nav {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
}

section.candidate-layout .nav-link {
    display: block;
    padding: 0.5rem 1rem;
}

section.candidate-layout .nav-link:hover,
section.candidate-layout .nav-link:focus {
    text-decoration: none;
}

section.candidate-layout .nav-link.disabled {
    color: #858796;
    pointer-events: none;
    cursor: default;
}

section.candidate-layout .nav-tabs {
    border-bottom: 1px solid #dddfeb;
}

section.candidate-layout .nav-tabs .nav-item {
    margin-bottom: -1px;
}

section.candidate-layout .nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: 0.35rem;
    border-top-right-radius: 0.35rem;
}

section.candidate-layout .nav-tabs .nav-link:hover,
section.candidate-layout .nav-tabs .nav-link:focus {
    border-color: #eaecf4 #eaecf4 #dddfeb;
}

section.candidate-layout .nav-tabs .nav-link.disabled {
    color: #858796;
    background-color: transparent;
    border-color: transparent;
}

section.candidate-layout .nav-tabs .nav-link.active,
section.candidate-layout .nav-tabs .nav-item.show .nav-link {
    color: #6e707e;
    background-color: #fff;
    border-color: #dddfeb #dddfeb #fff;
}

section.candidate-layout .nav-tabs .dropdown-menu {
    margin-top: -1px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

section.candidate-layout .nav-pills .nav-link {
    border-radius: 0.35rem;
}

section.candidate-layout .nav-pills .nav-link.active,
section.candidate-layout .nav-pills .show > .nav-link {
    color: #fff;
    background-color: #4e73df;
}

section.candidate-layout .nav-fill .nav-item {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    text-align: center;
}

section.candidate-layout .nav-justified .nav-item {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    text-align: center;
}

section.candidate-layout .tab-content > .tab-pane {
    display: none;
}

section.candidate-layout .tab-content > .active {
    display: block;
}

section.candidate-layout .navbar {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 0.5rem 1rem;
}

section.candidate-layout .navbar > .container,
section.candidate-layout .navbar > .container-fluid {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
}

section.candidate-layout .navbar-brand {
    display: inline-block;
    padding-top: 0.3125rem;
    padding-bottom: 0.3125rem;
    margin-right: 1rem;
    font-size: 1.25rem;
    line-height: inherit;
    white-space: nowrap;
}

section.candidate-layout .navbar-brand:hover,
section.candidate-layout .navbar-brand:focus {
    text-decoration: none;
}

section.candidate-layout .navbar-nav {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
}

section.candidate-layout .navbar-nav .nav-link {
    padding-right: 0;
    padding-left: 0;
}

section.candidate-layout .navbar-nav .dropdown-menu {
    position: static;
    float: none;
}

section.candidate-layout .navbar-text {
    display: inline-block;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

section.candidate-layout .navbar-collapse {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

section.candidate-layout .navbar-toggler {
    padding: 0.25rem 0.75rem;
    font-size: 1.25rem;
    line-height: 1;
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 0.35rem;
}

section.candidate-layout .navbar-toggler:hover,
section.candidate-layout .navbar-toggler:focus {
    text-decoration: none;
}

section.candidate-layout .navbar-toggler-icon {
    display: inline-block;
    width: 1.5em;
    height: 1.5em;
    vertical-align: middle;
    content: "";
    background: no-repeat center center;
    background-size: 100% 100%;
}

@media (max-width: 575.98px) {
    section.candidate-layout .navbar-expand-sm > .container,
    section.candidate-layout .navbar-expand-sm > .container-fluid {
        padding-right: 0;
        padding-left: 0;
    }
}

@media (min-width: 576px) {
    section.candidate-layout .navbar-expand-sm {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row nowrap;
        flex-flow: row nowrap;
        -webkit-box-pack: start;
        -ms-flex-pack: start;
        justify-content: flex-start;
    }

    section.candidate-layout .navbar-expand-sm .navbar-nav {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row;
    }

    section.candidate-layout .navbar-expand-sm .navbar-nav .dropdown-menu {
        position: absolute;
    }

    section.candidate-layout .navbar-expand-sm .navbar-nav .nav-link {
        padding-right: 0.5rem;
        padding-left: 0.5rem;
    }

    section.candidate-layout .navbar-expand-sm > .container,
    section.candidate-layout .navbar-expand-sm > .container-fluid {
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
    }

    section.candidate-layout .navbar-expand-sm .navbar-collapse {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important;
        -ms-flex-preferred-size: auto;
        flex-basis: auto;
    }

    section.candidate-layout .navbar-expand-sm .navbar-toggler {
        display: none;
    }
}

@media (max-width: 767.98px) {
    section.candidate-layout .navbar-expand-md > .container,
    section.candidate-layout .navbar-expand-md > .container-fluid {
        padding-right: 0;
        padding-left: 0;
    }
}

@media (min-width: 768px) {
    section.candidate-layout .navbar-expand-md {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row nowrap;
        flex-flow: row nowrap;
        -webkit-box-pack: start;
        -ms-flex-pack: start;
        justify-content: flex-start;
    }

    section.candidate-layout .navbar-expand-md .navbar-nav {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row;
    }

    section.candidate-layout .navbar-expand-md .navbar-nav .dropdown-menu {
        position: absolute;
    }

    section.candidate-layout .navbar-expand-md .navbar-nav .nav-link {
        padding-right: 0.5rem;
        padding-left: 0.5rem;
    }

    section.candidate-layout .navbar-expand-md > .container,
    section.candidate-layout .navbar-expand-md > .container-fluid {
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
    }

    section.candidate-layout .navbar-expand-md .navbar-collapse {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important;
        -ms-flex-preferred-size: auto;
        flex-basis: auto;
    }

    section.candidate-layout .navbar-expand-md .navbar-toggler {
        display: none;
    }
}

@media (max-width: 991.98px) {
    section.candidate-layout .navbar-expand-lg > .container,
    section.candidate-layout .navbar-expand-lg > .container-fluid {
        padding-right: 0;
        padding-left: 0;
    }
}

@media (min-width: 992px) {
    section.candidate-layout .navbar-expand-lg {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row nowrap;
        flex-flow: row nowrap;
        -webkit-box-pack: start;
        -ms-flex-pack: start;
        justify-content: flex-start;
    }

    section.candidate-layout .navbar-expand-lg .navbar-nav {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row;
    }

    section.candidate-layout .navbar-expand-lg .navbar-nav .dropdown-menu {
        position: absolute;
    }

    section.candidate-layout .navbar-expand-lg .navbar-nav .nav-link {
        padding-right: 0.5rem;
        padding-left: 0.5rem;
    }

    section.candidate-layout .navbar-expand-lg > .container,
    section.candidate-layout .navbar-expand-lg > .container-fluid {
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
    }

    section.candidate-layout .navbar-expand-lg .navbar-collapse {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important;
        -ms-flex-preferred-size: auto;
        flex-basis: auto;
    }

    section.candidate-layout .navbar-expand-lg .navbar-toggler {
        display: none;
    }
}

@media (max-width: 1199.98px) {
    section.candidate-layout .navbar-expand-xl > .container,
    section.candidate-layout .navbar-expand-xl > .container-fluid {
        padding-right: 0;
        padding-left: 0;
    }
}

@media (min-width: 1200px) {
    section.candidate-layout .navbar-expand-xl {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row nowrap;
        flex-flow: row nowrap;
        -webkit-box-pack: start;
        -ms-flex-pack: start;
        justify-content: flex-start;
    }

    section.candidate-layout .navbar-expand-xl .navbar-nav {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row;
    }

    section.candidate-layout .navbar-expand-xl .navbar-nav .dropdown-menu {
        position: absolute;
    }

    section.candidate-layout .navbar-expand-xl .navbar-nav .nav-link {
        padding-right: 0.5rem;
        padding-left: 0.5rem;
    }

    section.candidate-layout .navbar-expand-xl > .container,
    section.candidate-layout .navbar-expand-xl > .container-fluid {
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
    }

    section.candidate-layout .navbar-expand-xl .navbar-collapse {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important;
        -ms-flex-preferred-size: auto;
        flex-basis: auto;
    }

    section.candidate-layout .navbar-expand-xl .navbar-toggler {
        display: none;
    }
}

section.candidate-layout .navbar-expand {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
}

section.candidate-layout .navbar-expand > .container,
section.candidate-layout .navbar-expand > .container-fluid {
    padding-right: 0;
    padding-left: 0;
}

section.candidate-layout .navbar-expand .navbar-nav {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
}

section.candidate-layout .navbar-expand .navbar-nav .dropdown-menu {
    position: absolute;
}

section.candidate-layout .navbar-expand .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
}

section.candidate-layout .navbar-expand > .container,
section.candidate-layout .navbar-expand > .container-fluid {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
}

section.candidate-layout .navbar-expand .navbar-collapse {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
}

section.candidate-layout .navbar-expand .navbar-toggler {
    display: none;
}

section.candidate-layout .navbar-light .navbar-brand {
    color: rgba(0, 0, 0, 0.9);
}

section.candidate-layout .navbar-light .navbar-brand:hover,
section.candidate-layout .navbar-light .navbar-brand:focus {
    color: rgba(0, 0, 0, 0.9);
}

section.candidate-layout .navbar-light .navbar-nav .nav-link {
    color: rgba(0, 0, 0, 0.5);
}

section.candidate-layout .navbar-light .navbar-nav .nav-link:hover,
section.candidate-layout .navbar-light .navbar-nav .nav-link:focus {
    color: rgba(0, 0, 0, 0.7);
}

section.candidate-layout .navbar-light .navbar-nav .nav-link.disabled {
    color: rgba(0, 0, 0, 0.3);
}

section.candidate-layout .navbar-light .navbar-nav .show > .nav-link,
section.candidate-layout .navbar-light .navbar-nav .active > .nav-link,
section.candidate-layout .navbar-light .navbar-nav .nav-link.show,
section.candidate-layout .navbar-light .navbar-nav .nav-link.active {
    color: rgba(0, 0, 0, 0.9);
}

section.candidate-layout .navbar-light .navbar-toggler {
    color: rgba(0, 0, 0, 0.5);
    border-color: rgba(0, 0, 0, 0.1);
}

section.candidate-layout .navbar-light .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='rgba(0, 0, 0, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

section.candidate-layout .navbar-light .navbar-text {
    color: rgba(0, 0, 0, 0.5);
}

section.candidate-layout .navbar-light .navbar-text a {
    color: rgba(0, 0, 0, 0.9);
}

section.candidate-layout .navbar-light .navbar-text a:hover,
section.candidate-layout .navbar-light .navbar-text a:focus {
    color: rgba(0, 0, 0, 0.9);
}

section.candidate-layout .navbar-dark .navbar-brand {
    color: #fff;
}

section.candidate-layout .navbar-dark .navbar-brand:hover,
section.candidate-layout .navbar-dark .navbar-brand:focus {
    color: #fff;
}

section.candidate-layout .navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.5);
}

section.candidate-layout .navbar-dark .navbar-nav .nav-link:hover,
section.candidate-layout .navbar-dark .navbar-nav .nav-link:focus {
    color: rgba(255, 255, 255, 0.75);
}

section.candidate-layout .navbar-dark .navbar-nav .nav-link.disabled {
    color: rgba(255, 255, 255, 0.25);
}

section.candidate-layout .navbar-dark .navbar-nav .show > .nav-link,
section.candidate-layout .navbar-dark .navbar-nav .active > .nav-link,
section.candidate-layout .navbar-dark .navbar-nav .nav-link.show,
section.candidate-layout .navbar-dark .navbar-nav .nav-link.active {
    color: #fff;
}

section.candidate-layout .navbar-dark .navbar-toggler {
    color: rgba(255, 255, 255, 0.5);
    border-color: rgba(255, 255, 255, 0.1);
}

section.candidate-layout .navbar-dark .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='rgba(255, 255, 255, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

section.candidate-layout .navbar-dark .navbar-text {
    color: rgba(255, 255, 255, 0.5);
}

section.candidate-layout .navbar-dark .navbar-text a {
    color: #fff;
}

section.candidate-layout .navbar-dark .navbar-text a:hover,
section.candidate-layout .navbar-dark .navbar-text a:focus {
    color: #fff;
}

/* ----------------------------------------------- navbar ---------------------------------------------- */

/* ----------------------------------------------- dropdown ---------------------------------------------- */

section.candidate-layout .dropdown-toggle {
    white-space: nowrap;
}

section.candidate-layout .dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
    border-top: 0.3em solid;
    border-right: 0.3em solid transparent;
    border-bottom: 0;
    border-left: 0.3em solid transparent;
}

section.candidate-layout .dropdown-toggle:empty::after {
    margin-left: 0;
}

section.candidate-layout .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 10rem;
    padding: 0.5rem 0;
    margin: 0.125rem 0 0;
    font-size: 0.85rem;
    color: #858796;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
}

section.candidate-layout .dropdown-menu-left {
    right: auto;
    left: 0;
}

section.candidate-layout .dropdown-menu-right {
    right: 0;
    left: auto;
}

@media (min-width: 576px) {
    section.candidate-layout .dropdown-menu-sm-left {
        right: auto;
        left: 0;
    }

    section.candidate-layout .dropdown-menu-sm-right {
        right: 0;
        left: auto;
    }
}

@media (min-width: 768px) {
    section.candidate-layout .dropdown-menu-md-left {
        right: auto;
        left: 0;
    }

    section.candidate-layout .dropdown-menu-md-right {
        right: 0;
        left: auto;
    }
}

@media (min-width: 992px) {
    section.candidate-layout .dropdown-menu-lg-left {
        right: auto;
        left: 0;
    }

    section.candidate-layout .dropdown-menu-lg-right {
        right: 0;
        left: auto;
    }
}

@media (min-width: 1200px) {
    section.candidate-layout .dropdown-menu-xl-left {
        right: auto;
        left: 0;
    }

    section.candidate-layout .dropdown-menu-xl-right {
        right: 0;
        left: auto;
    }
}

section.candidate-layout .dropup .dropdown-menu {
    top: auto;
    bottom: 100%;
    margin-top: 0;
    margin-bottom: 0.125rem;
}

section.candidate-layout .dropup .dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
    border-top: 0;
    border-right: 0.3em solid transparent;
    border-bottom: 0.3em solid;
    border-left: 0.3em solid transparent;
}

section.candidate-layout .dropup .dropdown-toggle:empty::after {
    margin-left: 0;
}

section.candidate-layout .dropright .dropdown-menu {
    top: 0;
    right: auto;
    left: 100%;
    margin-top: 0;
    margin-left: 0.125rem;
}

section.candidate-layout .dropright .dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
    border-top: 0.3em solid transparent;
    border-right: 0;
    border-bottom: 0.3em solid transparent;
    border-left: 0.3em solid;
}

section.candidate-layout .dropright .dropdown-toggle:empty::after {
    margin-left: 0;
}

section.candidate-layout .dropright .dropdown-toggle::after {
    vertical-align: 0;
}

section.candidate-layout .dropleft .dropdown-menu {
    top: 0;
    right: 100%;
    left: auto;
    margin-top: 0;
    margin-right: 0.125rem;
}

section.candidate-layout .dropleft .dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
}

section.candidate-layout .dropleft .dropdown-toggle::after {
    display: none;
}

section.candidate-layout .dropleft .dropdown-toggle::before {
    display: inline-block;
    margin-right: 0.255em;
    vertical-align: 0.255em;
    content: "";
    border-top: 0.3em solid transparent;
    border-right: 0.3em solid;
    border-bottom: 0.3em solid transparent;
}

section.candidate-layout .dropleft .dropdown-toggle:empty::after {
    margin-left: 0;
}

section.candidate-layout .dropleft .dropdown-toggle::before {
    vertical-align: 0;
}

section.candidate-layout .dropdown-menu[x-placement^="top"],
section.candidate-layout .dropdown-menu[x-placement^="right"],
section.candidate-layout .dropdown-menu[x-placement^="bottom"],
section.candidate-layout .dropdown-menu[x-placement^="left"] {
    right: auto;
    bottom: auto;
}

section.candidate-layout .dropdown-divider {
    height: 0;
    margin: 0.5rem 0;
    overflow: hidden;
    border-top: 1px solid #eaecf4;
}

section.candidate-layout .dropdown-item {
    display: block;
    width: 100%;
    padding: 0.25rem 1.5rem;
    clear: both;
    font-weight: 400;
    color: #3a3b45;
    text-align: inherit;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
}

section.candidate-layout .dropdown-item:hover,
section.candidate-layout .dropdown-item:focus {
    color: #2e2f37;
    text-decoration: none;
    background-color: #f8f9fc;
}

section.candidate-layout .dropdown-item.active,
section.candidate-layout .dropdown-item:active {
    color: #fff;
    text-decoration: none;
    background-color: #4e73df;
}

section.candidate-layout .dropdown-item.disabled,
section.candidate-layout .dropdown-item:disabled {
    color: #858796;
    pointer-events: none;
    background-color: transparent;
}

section.candidate-layout .dropdown-menu.show {
    display: block;
}

section.candidate-layout .dropdown-header {
    display: block;
    padding: 0.5rem 1.5rem;
    margin-bottom: 0;
    font-size: 0.875rem;
    color: #858796;
    white-space: nowrap;
}

section.candidate-layout .dropdown-item-text {
    display: block;
    padding: 0.25rem 1.5rem;
    color: #3a3b45;
}

/* ----------------------------------------------- nav -item ---------------------------------------------- */
section.candidate-layout .dropdown .dropdown-menu {
    font-size: 0.85rem;
}

section.candidate-layout .dropdown .dropdown-menu .dropdown-header {
    font-weight: 800;
    font-size: 0.65rem;
    color: #b7b9cc;
}

section.candidate-layout .no-arrow .dropdown-toggle::after {
    display: none;
}

section.candidate-layout .sidebar .nav-item.dropdown .dropdown-toggle::after,
section.candidate-layout .topbar .nav-item.dropdown .dropdown-toggle::after {
    width: 1rem;
    text-align: center;
    float: right;
    vertical-align: 0;
    border: 0;
    font-weight: 900;
    content: "\f105";
    font-family: "Font Awesome 5 Free";
}

section.candidate-layout
    .sidebar
    .nav-item.dropdown.show
    .dropdown-toggle::after,
section.candidate-layout
    .topbar
    .nav-item.dropdown.show
    .dropdown-toggle::after {
    content: "\f107";
}

section.candidate-layout .sidebar .nav-item .nav-link,
section.candidate-layout .topbar .nav-item .nav-link {
    position: relative;
}

section.candidate-layout .sidebar .nav-item .nav-link {
    width: 100% !important;
}

section.candidate-layout .sidebar .nav-item .nav-link .badge-counter,
section.candidate-layout .topbar .nav-item .nav-link .badge-counter {
    position: absolute;
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
    -webkit-transform-origin: top right;
    transform-origin: top right;
    right: 0.25rem;
    margin-top: -0.25rem;
}

section.candidate-layout .sidebar .nav-item .nav-link .img-profile,
section.candidate-layout .topbar .nav-item .nav-link .img-profile {
    height: 2rem;
    width: 2rem;
}

section.candidate-layout .topbar {
    height: 4.375rem;
}

/* section.candidate-layout .topbar #sidebarToggleTop {
    height: 2.5rem;
    width: 2.5rem;
}

section.candidate-layout .topbar #sidebarToggleTop:hover {
    background-color: transparent;
}

section.candidate-layout .topbar #sidebarToggleTop:active {
    background-color: transparent;
} */

section.candidate-layout .topbar .navbar-search {
    width: 25rem;
}

section.candidate-layout .topbar .navbar-search input {
    font-size: 0.85rem;
}

section.candidate-layout .topbar .topbar-divider {
    width: 0;
    border-right: 1px solid #e3e6f0;
    height: calc(4.375rem - 2rem);
    margin: auto 1rem;
}

section.candidate-layout .topbar .nav-item .nav-link {
    height: 4.375rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 0 0.75rem;
}

section.candidate-layout .topbar .nav-item .nav-link:focus {
    outline: none;
}

section.candidate-layout .topbar .nav-item:focus {
    outline: none;
}

section.candidate-layout .topbar .dropdown {
    position: static;
}

section.candidate-layout .topbar .dropdown .dropdown-menu {
    width: calc(100% - 1.5rem);
    right: 0.75rem;
}

section.candidate-layout .topbar .dropdown-list {
    padding: 0;
    border: none;
    overflow: hidden;
}

section.candidate-layout .topbar .dropdown-list .dropdown-header {
    background-color: #4e73df;
    border: 1px solid #4e73df;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    color: #fff;
}

section.candidate-layout .topbar .dropdown-list .dropdown-item {
    white-space: normal;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    border-left: 1px solid #e3e6f0;
    border-right: 1px solid #e3e6f0;
    border-bottom: 1px solid #e3e6f0;
    line-height: 1.3rem;
}

section.candidate-layout
    .topbar
    .dropdown-list
    .dropdown-item
    .dropdown-list-image {
    position: relative;
    height: 2.5rem;
    width: 2.5rem;
}

section.candidate-layout
    .topbar
    .dropdown-list
    .dropdown-item
    .dropdown-list-image
    img {
    height: 2.5rem;
    width: 2.5rem;
}

section.candidate-layout
    .topbar
    .dropdown-list
    .dropdown-item
    .dropdown-list-image
    .status-indicator {
    background-color: #eaecf4;
    height: 0.75rem;
    width: 0.75rem;
    border-radius: 100%;
    position: absolute;
    bottom: 0;
    right: 0;
    border: 0.125rem solid #fff;
}

section.candidate-layout .topbar .dropdown-list .dropdown-item .text-truncate {
    max-width: 10rem;
}

section.candidate-layout .topbar .dropdown-list .dropdown-item:active {
    background-color: #eaecf4;
    color: #3a3b45;
}

@media (min-width: 576px) {
    section.candidate-layout .topbar .dropdown {
        position: relative;
    }

    section.candidate-layout .topbar .dropdown .dropdown-menu {
        width: auto;
        right: 0;
    }

    section.candidate-layout .topbar .dropdown-list {
        width: 20rem !important;
    }

    section.candidate-layout
        .topbar
        .dropdown-list
        .dropdown-item
        .text-truncate {
        max-width: 13.375rem;
    }
}

section.candidate-layout .topbar.navbar-light .navbar-nav .nav-item .nav-link {
    color: #d1d3e2;
}

section.candidate-layout
    .topbar.navbar-light
    .navbar-nav
    .nav-item
    .nav-link:hover {
    color: #b7b9cc;
}

section.candidate-layout
    .topbar.navbar-light
    .navbar-nav
    .nav-item
    .nav-link:active {
    color: #858796;
}

/* bg-color */

section.candidate-layout .bg-theme {
    background-color: var(--secondary);
}

section.candidate-layout .ant-switch-checked.switch-theme,
section.candidate-layout .ant-switch-checked.switch-theme:hover,
section.candidate-layout .ant-switch-checked.switch-theme:focus {
    background-color: var(--primary) !important;
}

section.candidate-layout .ant-switch-checked.switch-theme:disabled {
    background-color: var(--secondary) !important;
}

section.candidate-layout .text-theme {
    color: var(--theme-primary-color);
}

section.candidate-layout .text-theme-secondary {
    color: var(--secondary);
}

section.candidate-layout .btn-theme,
section.candidate-layout .btn-theme:focus {
    background-color: var(--theme-primary-color);
    border: 1px solid var(--theme-primary-color);
    color: #ffffff;
}

section.candidate-layout .btn-theme:hover {
    background-color: var(--primary-hover) !important;
    border: 1px solid var(--primary-hover) !important;
    color: #ffffff !important;
}

section.candidate-layout .btn-theme-secondary,
section.candidate-layout .btn-theme-secondary:focus {
    background-color: var(--secondary);
    border: 1px solid var(--secondary);
    color: #ffffff;
}

section.candidate-layout .btn-theme-secondary:hover {
    background-color: var(--secondary-dark) !important;
    border: 1px solid var(--secondary-dark) !important;
    color: #ffffff !important;
}

section.candidate-layout .btn {
    padding: 5px 15px;
    border-radius: 4px;
    background: var(--theme-primary-color);
    color: #fff;
    height: 38px;
}

section.candidate-layout .candidate-list-card .btn {
    border-radius: 4px;
    background: #fff;
    color: var(--theme-primary-color);
    border-color: #ececec;
    height: 32px;
    padding: 4px 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

section.candidate-layout .candidate-list-card a.btn {
    background: #f4f6fe;
    color: var(--theme-primary-color) !important;
    border: #f4f6fe;
    height: 32px;
}

section.candidate-layout .ant-btn.btn {
    padding: 0px 15px;
}

/*  --------- table start ----- */
section.candidate-layout .table-responsive.opportunity-type-list,
section.candidate-layout .table-responsive.employee-list {
    height: calc(100vh - 390px);
    min-height: 100px;
    /* border: 0.5px solid #e3e6f0; */
}

section.candidate-layout .table-responsive.location-list,
section.candidate-layout .table-responsive.interview-list,
section.candidate-layout .table-responsive.opportunity-list,
section.candidate-layout .table-responsive.department-list,
section.candidate-layout .table-responsive.sub-department-list,
section.candidate-layout .table-responsive.business-list,
section.candidate-layout .table-responsive.email-template-list,
section.candidate-layout .table-responsive.offer-letter-list {
    height: calc(100vh - 316px);
    min-height: 100px;
    /* border: 0.5px solid #e3e6f0; */
}

section.candidate-layout .table-responsive.location-list.no-records,
section.candidate-layout .table-responsive.interview-list.no-records,
section.candidate-layout .table-responsive.opportunity-list.no-records,
section.candidate-layout .table-responsive.opportunity-type-list.no-records,
section.candidate-layout .table-responsive.employee-list.no-records,
section.candidate-layout .table-responsive.department-list.no-records,
section.candidate-layout .table-responsive.sub-department-list.no-records,
section.candidate-layout .table-responsive.business-list.no-records {
    height: auto;
    min-height: auto;
    /* border: 0.5px solid #e3e6f0; */
}

section.candidate-layout .table-responsive.location-list table,
section.candidate-layout .table-responsive.interview-list table,
section.candidate-layout .table-responsive.opportunity-list table,
section.candidate-layout .table-responsive.opportunity-type-list table,
section.candidate-layout .table-responsive.employee-list table,
section.candidate-layout .table-responsive.department-list table,
section.candidate-layout .table-responsive.sub-department-list table,
section.candidate-layout .table-responsive.business-list table,
section.candidate-layout .table-responsive.email-template-list table,
section.candidate-layout .table-responsive.offer-letter-list table {
    margin-bottom: 0px !important;
}

.table-responsive.location-list table,
.table-responsive.interview-list table,
.table-responsive.opportunity-list table,
.table-responsive.opportunity-type-list table,
.table-responsive.employee-list table,
.table-responsive.department-list table,
.table-responsive.sub-department-list table,
.table-responsive.business-list table,
.table-responsive.email-template-list table,
.table-responsive.offer-letter-list table {
    margin-bottom: 0px !important;
}

.table-responsive.location-list:not(.no-records) table thead th,
.table-responsive.interview-list:not(.no-records) table thead th,
.table-responsive.opportunity-type-list:not(.no-records) table thead th,
.table-responsive.employee-list:not(.no-records) table thead th,
.table-responsive.department-list:not(.no-records) table thead th,
.table-responsive.sub-department-list:not(.no-records) table thead th,
.table-responsive.business-list:not(.no-records) table thead th,
.table-responsive.email-template-list:not(.no-records) table thead th,
.table-responsive.offer-letter-list:not(.no-records) table thead th {
    position: sticky;
    top: 0;
    z-index: 999;
    font-weight: 600;
    text-transform: capitalize;
}
/*  ---------  table end  ----- */

section.candidate-layout .resume-card {
    min-height: calc(100vh - 150px);
    padding-bottom: 5px !important;
}

section.candidate-layout .resume-detail-overflow {
    overflow-x: auto;
    height: calc(100vh - 250px);
}

section.candidate-layout .resume-detail-overflow .item-array > .item-object {
    margin-left: 20px;
    margin-top: 15px;
}

section.candidate-layout .resume-detail-overflow .item-array > .item-paragraph {
    /* Target the parent directly following the element with class "some" */
    margin: 0 0 0 20px;
    padding: 0;
}

section.candidate-layout input.search-input {
    width: 100%;
    border-radius: 4px;
    padding: 5px 15px;
    background: #fafafa;
    height: 38px;
    box-shadow: none !important;
    outline: none !important;
    border: 1px solid #ececec !important;
}

section.candidate-layout input.search-input:focus {
    border: 1px solid #111111 !important;
}

section.candidate-layout .mx-w-100px {
    max-width: 100px;
}

section.candidate-layout .candidate-list-wrap .brief .badge {
    font-size: 14px;
    padding: 5px 10px;
    font-weight: 400;
    background-color: var(--theme-primary-color) !important;
}

section.candidate-layout .candidate-list-wrap {
    background-color: #fafafa;
}

section.candidate-layout .candidate-list-wrap .brief p,
section.candidate-layout p.text-heading-candidate {
    /* line-height: 1; */
    font-weight: 400;
}

section.candidate-layout .candidate-list-wrap .brief .heading-clr {
    font-size: 16px;
}

section.candidate-layout button.filter-btn {
    width: max-content;
}

@media screen and (max-width: 1442px) {
    section.candidate-layout .candidate-list-wrap .brief p {
        font-size: 14px;
    }
    section.candidate-layout .candidate-list-wrap .row > * {
        padding-left: 12px !important;
        padding-right: 12px !important;
    }
    section.candidate-layout .candidate-list-wrap .candidate-list-card {
        padding: 10px !important;
    }
}

section.candidate-layout .node-settings .group-relative {
    margin-bottom: 0px !important;
}

section.candidate-layout .employee-role-select {
    min-width: 180px;
}

section.candidate-layout .node-settings .node-setting-list {
    height: calc(100vh - 360px);
    min-height: 100px;
    /* border: 0.5px solid #e3e6f0; */
    overflow-x: auto;
}

/* section.candidate-layout .new-candidate .type-icon,
section.candidate-layout .edit-candidate .type-icon {
    max-width: 50px !important;
    min-width: 50px !important;
}

section.candidate-layout .new-candidate .edit-icons,
section.candidate-layout .edit-candidate .edit-icons {
    max-width: 100px !important;
    min-width: 100px !important;
} */

/* section.candidate-layout .skill-row:last-child,
section.candidate-layout .education-row:last-child,
section.candidate-layout .experience-row:last-child {
    margin: 0;
} */

section.candidate-layout .skill-row,
section.candidate-layout .education-row,
section.candidate-layout .experience-row {
    /* margin: 5px 10px; */
    border-radius: 8px;
    padding: 12px 20px;
    background-color: #f9f9f9;
    min-height: 40px;
    margin: 15px 0 0;
}

.education-row .box {
    display: flex;
    row-gap: 10px;
    column-gap: 20px;
}

.education-row .box .content .head {
    display: inline-flex;
    /* flex-wrap: wrap; */
    /* align-items: center; */
    row-gap: 10px;
    flex-direction: column;
}

.education-row .box .content {
    padding: 0 !important;
}

.education-row .box .content .head ul,
.candidate-responsibilities {
    padding: 0;
    margin: 5px 0 0;
    list-style: none;
}

.education-row .box .content .head ul li,
.candidate-responsibilities li {
    padding-left: 17px;
    position: relative;
}

.education-row .box .content .head ul li::after,
.candidate-responsibilities li::after {
    content: "";
    width: 6px;
    height: 6px;
    background: #d9d9d9;
    position: absolute;
    left: 0;
    top: 8px;
    border-radius: 50px;
}

section.candidate-layout .edit-candidate button.add-more {
    background: transparent;
    padding: 0;
    height: auto;
    border: none;
    color: var(--theme-primary-color);
    text-decoration: underline;
}

section.candidate-layout .edit-candidate button.add-more:hover {
    color: var(--theme-primary-color);
}

section.candidate-layout span.currently-working {
    color: #ffffff;
    background-color: #05b6a6;
    font-size: 14px;
    margin-left: 10px;
    padding: 5px 10px;
    border-radius: 25px;
    display: inline-block;
}

section.candidate-layout .new-candidate p.skill-info,
section.candidate-layout .new-candidate p.experience-info,
section.candidate-layout .new-candidate p.education-info,
section.candidate-layout .edit-candidate p.skill-info,
section.candidate-layout .edit-candidate p.experience-info,
section.candidate-layout .edit-candidate p.education-info {
    margin: 0;
}

section.candidate-layout .new-candidate p.experience-info,
section.candidate-layout .edit-candidate p.experience-info {
    margin: 0;
    color: #c6c6c6;
}

section.candidate-layout .new-candidate span.skill-name,
section.candidate-layout .edit-candidate span.skill-name {
    font-weight: 500;
}

section.candidate-layout .new-candidate span.skill-name,
section.candidate-layout .new-candidate span.skill-type,
section.candidate-layout .new-candidate .experience-info span,
section.candidate-layout .edit-candidate span.skill-name,
section.candidate-layout .edit-candidate span.skill-type,
section.candidate-layout .edit-candidate .experience-info span {
    color: #111111;
    margin: 0;
}

section.candidate-layout .new-candidate span.divider,
section.candidate-layout .edit-candidate span.divider {
    background-color: #c6c6c6;
    height: 6px;
    width: 6px;
    border-radius: 50%;
    display: inline-block;
    margin: 0px 6px;
}

section.candidate-layout .new-candidate .add-more-detail,
section.candidate-layout .edit-candidate .add-more-detail {
    color: #c6c6c6;
}

section.candidate-layout .interview .interview-status,
section.candidate-layout .interview .interview-status.schedule {
    background-color: #4caf50; /* Green for scheduled */
    padding: 5px 10px;
    border-radius: 20px;
    color: #ffffff;
}

section.candidate-layout .interview .interview-status.disqualified,
section.candidate-layout .interview .interview-status.rejected,
section.candidate-layout .interview .interview-status.cancelled {
    background-color: #f44336; /* Red for canceled */
}

section.candidate-layout .interview .interview-status.completed {
    background-color: #8bc34a; /* Light green for completed */
}

section.candidate-layout .interview .interview-status.rescheduled {
    background-color: #2196f3; /* Blue for rescheduled */
}

section.candidate-layout .interview .interview-status.awaiting_feedback {
    background-color: #ffc107; /* Yellow for awaiting feedback */
}

section.candidate-layout .candidate-feedback .interview-round {
    background-color: #f0f0f0;
    padding: 3px 20px;
    border-radius: 20px;
}

section.candidate-layout .candidate-feedback .text-placeholder {
    color: #8c8c8c;
}

section.candidate-layout .candidate-profile-tabs li.nav-item {
    margin: 0;
}

section.candidate-layout .candidate-profile-tabs li.nav-item button {
    border: none;
    border-bottom: 0px solid;
    color: #8c8c8c;
}

section.candidate-layout .nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: 0.35rem;
    border-top-right-radius: 0.35rem;
}

section.candidate-layout .candidate-profile-tabs li.nav-item button.active {
    border-color: var(--theme-primary-color);
    border-bottom-width: 2px;
    color: var(--theme-primary-color);
    font-weight: 500;
}

section.candidate-layout .technical-skill > li {
    margin: 0 0 10px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 10px;
}

section.candidate-layout .technical-skill > li:last-child {
    margin: 0;
    border: none;
    padding: 0;
}

section.candidate-layout .technical-skill > li ul li {
    background: #eeeeee;
    border-radius: 4px;
    padding: 2px 10px;
}

section.candidate-layout .technical-skill > li ul {
    margin: 5px 0 0;
}

section.candidate-layout .soft-skill {
    display: inline-flex;
    gap: 8px;
    flex-wrap: wrap;
}

section.candidate-layout .soft-skill li {
    background: #eeeeee;
    border-radius: 4px;
    padding: 2px 10px;
    margin: 0;
}

section.candidate-layout .candidate-document-card {
    border: 1px solid #eeeeee;
    border-radius: 10px;
    padding: 10px 10px;
}

section.candidate-layout .candidate-document-logo {
    margin-right: 10px;
    border-radius: 5px;
}

section.candidate-layout .candidate-documents {
    box-shadow: 0px 6px 16px 0px #00000014;
    max-width: 600px;
    border-radius: 16px;
    margin: 30px auto;
    background: #fff;
}

section.candidate-layout .candidate-documents .document-row {
    border-bottom: 1px solid #efefef;
    padding: 10px 15px 20px 15px;
    width: 100%;
}

section.candidate-layout .candidate-documents .required-field {
    color: red;
    margin-left: 5px;
}

section.candidate-layout .candidate-documents label.main-label {
    border-bottom: 1px solid #efefef;
    padding: 15px 20px;
    width: 100%;
    font-size: 18px;
    font-weight: 600 !important;
}

section.candidate-layout .candidate-documents label.file-label {
    width: 100%;
    font-size: 14px;
    font-weight: 600 !important;
}

section.candidate-layout .file-uploader {
    width: 100%;
}

section.candidate-layout .ant-upload-text {
    font-size: 10px !important;
}

section.candidate-layout .document-picker {
    padding: 20px;
}

section.candidate-layout .upload-label label {
    font-size: 14px;
    color: #262626;
}

section.candidate-layout .upload-label label span {
    font-size: 14px;
    color: #4864e1;
}

section.candidate-layout .upload-file-name {
    font-size: 14px;
    padding: 0px;
}

section.candidate-layout .upload-file-name .ant-list-item-meta-title span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

section.candidate-layout .remove-document {
    cursor: pointer;
    border: none;
    background: transparent;
}

section.candidate-layout .spinner {
    width: 15px;
    height: 15px;
    border-width: 2px;
}

section.candidate-layout .error-message {
    font-size: 14px;
    color: red;
    margin: 0;
    padding: 0;
    margin-top: 5px;
}

section.candidate-layout .submit-btn {
    background-color: #4864e1;
    color: #efefef;
}

section.candidate-layout .submit-btn:hover,
section.candidate-layout .submit-btn:focus {
    background-color: #4864e1;
    color: #efefef;
}

section.candidate-layout span.document-name {
    font-size: smaller;
}

section.candidate-layout span.max-documents {
    font-size: smaller;
    margin-left: 10px;
}

section.candidate-layout .schedule-interview {
    display: flex;
    justify-content: space-between;
}

section.candidate-layout .schedule-interview-date {
    width: 50%;
    padding-top: 5px;
    padding-left: 9px;
}

section.candidate-layout .schedule-interview-link {
    width: 50%;
    display: flex;
    flex-direction: column;
    padding-top: 5px;
    padding-left: 9px;
}

section.candidate-layout .professional-summary p,
section.candidate-layout .candi-experience p {
    color: #111111;
}

section.candidate-layout .education-row .box .content .head ul li:after,
section.candidate-layout .candidate-responsibilities li:after {
    background: transparent;
}

section.candidate-layout .matching-jobs .candidate-list-card a.btn,
section.candidate-layout .matching-jobs .candidate-list-card button.btn {
    height: 38px;
    border-radius: 6px !important;
    font-weight: 500 !important;
}

section.candidate-layout .matching-jobs .candidate-list-card a.btn.btn-primary,
section.candidate-layout
    .matching-jobs
    .candidate-list-card
    button.btn.btn-primary {
    background: var(--theme-primary-color);
    color: #fff !important;
}

section.candidate-layout
    .matching-jobs
    .candidate-list-card
    button.btn.btn-light:hover,
section.candidate-layout
    .matching-jobs
    .candidate-list-card
    button.btn.btn-light:active {
    color: var(--theme-primary-color) !important;
}

section.candidate-layout .coding-editor {
    background: #000102;
}

section.candidate-layout .coding-editor h5 {
    color: #ffffff;
    padding: 10px 5px;
}

section.candidate-layout .coding-editor .monaco {
    min-height: 400px;
}

section.candidate-layout .pagination-button {
    padding: 20px 0 0;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

section.candidate-layout .question-options label {
    display: block;
    margin-top: 5px;
}

section.candidate-layout .question-options label input {
    margin-right: 5px;
}

section.candidate-layout .test-cases-box {
    margin-top: 20px;
    background: aliceblue;
    padding: 10px 25px;
    border-radius: 10px;
}

section.candidate-layout .test-cases-box h5 {
    font-weight: bolder;
}

section.candidate-layout .test-cases-box .test-case {
    margin-bottom: 5px;
}

section.candidate-layout .test-cases-box .test-case p {
    margin: 0px;
}

section.candidate-layout pre.interview-question {
    margin: 0px;
    text-wrap: wrap;
    font-weight: 800;
    font-size: 1rem;
    font-family: "Robots", sans-serif;
}

.warning-modal svg {
    fill: #cd1b1b;
}

.warning-modal .content-box h4 {
    font-weight: 600;
    font-size: 22px;
    color: #000;
    margin: 0;
}

.warning-modal .content-box .note {
    color: #dc2424;
    font-weight: 600;
    margin-top: 10px;
}

.warning-modal {
    padding: 55px 0 25px;
}

.warning-modal > span {
    min-width: 60px;
    width: 60px;
    height: 60px;
    background: rgba(220, 36, 36, 0.2);
    border-radius: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.warning-modal .content-box {
    flex: 1;
}

.warning-modal .content-box .flex-box {
    display: flex;
    align-items: center;
    margin: 0 0 8px;
    justify-content: space-between;
    flex-wrap: wrap;
}

.warning-modal .count span {
    min-width: 48px;
    height: 48px;
    background: #dc2424;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50px;
    color: #fff;
    font-weight: 600;
    font-size: 18px;
}

.warning-modal .count {
    align-items: center;
    gap: 5px;
    font-size: 16px;
    color: #8f8f8f;
    flex-direction: column;
    display: flex;
}

.warning-modal-opacity {
    background-color: rgba(0, 0, 0, 0.6) !important;
}