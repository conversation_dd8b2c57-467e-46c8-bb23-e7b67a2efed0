from enum import Enum
from peewee import Field


class EnumField(Field):
    def __init__(self, enum, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.enum = enum

    def db_value(self, value):
        if value is None:
            return None
        return value.value if isinstance(value, self.enum) else value

    def python_value(self, value):
        if value is None:
            return None
        return self.enum(value)


class EnumClass(Enum):
    def __str__(self):
        return str(self.value)

    @property
    def name(self):
        return self._name_.replace("_", " ")

    @classmethod
    def options_list(cls):
        return [
            {"label": status.name.replace("_", " "), "value": status.value}
            for status in cls
        ]


class CandidateStatus(EnumClass):
    Created = 0
    Pending_Review = 1
    Shortlisted = 2
    Rejected = 3
    Scheduled_for_Interview = 4
    Hired = 5
    Offer_Extended = 6
    Contract_Signed = 7
    Withdrawn = 8
    No_Action = 9
    Blacklist = 10


class InterviewStatus(EnumClass):
    Cancelled = 0
    Scheduled = 1
    Rescheduled = 2
    Rejected = 3
    Passed = 4
    NoShow = 5
    Awaiting_Feedback = 6
    Disqualified = 7


class CandidateInterviewStatus(EnumClass):
    Cancelled = 0
    Scheduled = 1
    Rejected = 2
    Finalize = 3
    Selected = 4
    Disqualified = 5


class InterviewMode(EnumClass):
    Video_Call = 0
    Face_to_Face = 1
    Telephonic = 2
    Screening = 3


class EmailTemplateType(EnumClass):
    Job_Template = 0
    Offer_Template = 1
    UserResetPassword = 2
    EmployeeResetPassword = 3
    EmployeeInterviewReminder = 4
    EmployeeRegistration = 5
    EmployeeOTPResend = 6
    EmployeeScheduleInterview = 7
    ContactEnquiry = 8
    CandidateOfferLetter = 9
    ESignOTPVerification =10
    ResendOTPNotificationBusiness = 11
    BusinessEmailVerificationOTP = 12
    CandidateCustomLayout = 13
    CandidateForgotPassword = 14
    CandidateInterviewReminder = 15
    CandidateResendOTP = 16
    CandidateScheduleInterviewMail = 17
    CandidateUploadDocuments = 18
    UserResendOTP = 19
    UserVerifyEmail = 20
    
class QuestionType(EnumClass):
    objective = 0
    subjective = 1
    coding = 2


class InterviewFeedbackStatus(EnumClass):
    Qualified = 0
    NotQualified = 1


class OfferLetterTemplateType(EnumClass):
    Standard = 0
    Executive = 1
    Internship = 2
    Contract = 3
    Remote = 4
