from peewee import (
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>Field,
    DateTimeField,
    TextField,
    SQL,
    BigIntegerField,
    ForeignKeyField,
    SmallIntegerField,
)
from app.models.base import ActiveRecord
from pydantic import BaseModel
from app.models.concern.enum import OfferLetterTemplateType, EnumField


class OfferLetterTemplate(ActiveRecord):
    # to prevent circular import
    from app.models.business import Business
    from app.models.employee import Employee

    id = AutoField()
    template_name = CharField(max_length=255)
    template_content = TextField()
    position = CharField(max_length=255, null=True)
    department = CharField(max_length=255, null=True)
    status = SmallIntegerField(constraints=[SQL("DEFAULT 1")])
    is_default = SmallIntegerField(constraints=[SQL("DEFAULT 0")])
    template_type = EnumField(OfferLetterTemplateType, default=OfferLetterTemplateType.Standard)

    business_id = BigIntegerField(null=True)
    created_by_id = BigIntegerField(null=True)
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")])

    # associations
    business = ForeignKeyField(
        Business, null=True, backref="offer_letter_templates", lazy_load=True
    )
    created_by = ForeignKeyField(
        Employee, null=True, backref="created_offer_letter_templates", lazy_load=True
    )

    def info(self):
        return {
            "id": self.id,
            "template_name": self.template_name,
            "template_content": self.template_content,
            "position": self.position,
            "department": self.department,
            "status": self.status,
            "is_default": self.is_default,
            "template_type": self.template_type.value if self.template_type else None,
            "business_id": self.business_id,
            "created_by_id": self.created_by_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    class Meta:
        table_name = "offer_letter_templates"


class OfferLetterTemplateCreate(BaseModel):
    template_name: str
    template_content: str
    position: str = None
    department: str = None
    template_type: str = "Standard"


class OfferLetterTemplateUpdate(BaseModel):
    template_name: str = None
    template_content: str = None
    position: str = None
    department: str = None
    status: int = None
    template_type: str = None
