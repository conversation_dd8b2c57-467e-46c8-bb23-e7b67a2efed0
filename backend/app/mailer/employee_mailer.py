from app.models import Employee, ScheduleInterview, Candidate, Business, EmailTemplate
from app.utils import AppTemplates, build_subdomain_url
from app.config import API_URL, APP_URL
from app.mailer.base_mailer import BaseMailer
import mimetypes
import os
import requests
import logging
from app.models.concern.enum import EmailTemplateType
from jinja2 import Template, Environment, exceptions as jinja_exceptions
from app.helper.mailer_helper import MailerHelper

class EmployeeMailer(BaseMailer):
    @classmethod
    def reset_password_email(cls, employee: Employee):
        logging.info("Inside Employee Mailer reset_password_email")

        subject = "Recruitease Pro - Password Reset OTP"
        context = {
            "request": None,
            "employee": employee,
            "api_url": API_URL,
            "app_url": APP_URL,
        }
        logging.info(EmailTemplateType.EmployeeResetPassword)
        logging.info(employee.business_id)

        # Step 1: Try fetching template specific to the business
        template_obj = EmailTemplate.get_or_none(
            (EmailTemplate.template_type == EmailTemplateType.EmployeeResetPassword) &
            (EmailTemplate.business_id == employee.business_id) &
            (EmailTemplate.status == 1)
        )
        logging.info("template_obj")
        logging.info(template_obj)

        # Step 2: Raise error if no template at all is found
        if not template_obj:
            raise Exception("Email template for Employee Reset Password not found.")

        # Step 3: Render email_body using Jinja2
        jinja_template = Template(template_obj.email_body)
        rendered_body = jinja_template.render(context)

        # Step 4: Send email
        cls.send_email(subject, employee.email, rendered_body, send_to_primary=True)

    @classmethod
    def resend_otp_email(cls, employee: Employee, resend_text: str):
        subject = "Recruitease Pro - New OTP"
        context = {
            "request": None,
            "employee": employee,
            "api_url": API_URL,
            "app_url": APP_URL,
            "resend_text": resend_text,
        }
    
    @classmethod
    def new_employee_email(cls, employee: Employee, password: str):
        logging.info("Inside Employee new_employee_email")

        subject = f"Recruitease Pro - Welcome to {employee.business.name}"
        login_url = build_subdomain_url(APP_URL, employee.business.subdomain)

        context = {
            "request": None,
            "employee": employee,
            "api_url": API_URL,
            "app_url": APP_URL,
            "login_url": login_url,
            "password": password,
        }

        logging.info(EmailTemplateType.EmployeeRegistration)
        logging.info(employee.business_id)

        # Step 1: Try fetching template specific to the business
        template_obj = EmailTemplate.get_or_none(
            (EmailTemplate.template_type == EmailTemplateType.EmployeeRegistration) &
            (EmailTemplate.business_id == employee.business_id) &
            (EmailTemplate.status == 1)
        )
        logging.info("template_obj")
        logging.info(template_obj)

        # Step 2: Raise error if no template is found
        if not template_obj:
            raise Exception("Email template for Employee Registration not found.")

        # Step 3: Render email_body using Jinja2
        jinja_template = Template(template_obj.email_body)
        rendered_body = jinja_template.render(context)

        # Step 4: Send email
        cls.send_email(subject, employee.email, rendered_body, send_to_primary=True)
   
    @classmethod
    def interview_scheduled_mail(cls, employee: Employee, schedule_interview: ScheduleInterview):
        logging.info("Inside Employee interview_scheduled_mail")

        subject = "Recruitease Pro - Interview Assigned"
        business: Business = employee.business
        candidate: Candidate = schedule_interview.candidate

        attachments = []

        # Attach resume if available
        if candidate.resume:
            try:
                response = requests.get(candidate.resume_url)
                filename = os.path.basename(candidate.resume_url)

                mime_type, _ = mimetypes.guess_type(filename)
                if mime_type is None:
                    mime_type = "application/octet-stream"

                file_bytes = response.content
                attachments.append((filename, file_bytes, mime_type))
            except Exception as e:
                logging.error(f"Failed to fetch resume: {e}")

        has_attachment = len(attachments) > 0

        context = {
            "request": None,
            "api_url": API_URL,
            "app_url": APP_URL,
            "candidate": candidate,
            "employee_name": employee.full_name(),
            "business": business,
            "has_attachment": has_attachment,
            "schedule_interview": schedule_interview,
        }

        logging.info(f"Context values: {list(context.values())}")
        logging.info(f"EmailTemplateType: {EmailTemplateType.EmployeeScheduleInterview}")
        logging.info(f"Business ID: {business.id}")

        # Step 1: Try fetching template specific to the business
        template_obj = EmailTemplate.get_or_none(
            (EmailTemplate.template_type == EmailTemplateType.EmployeeScheduleInterview) &
            (EmailTemplate.business_id == business.id) &
            (EmailTemplate.status == 1)
        )
        if not template_obj:
            raise Exception("Email template for Schedule Interview not found.")

        logging.info("Fetched template body (truncated):")
        logging.info(template_obj.email_body[:500])  # Avoid dumping large HTML

        # Step 2: Render email_body using Jinja2 Environment
        try:
            env = Environment()
            env.filters["to_timezone"] = MailerHelper.to_timezone
            template = env.from_string(template_obj.email_body)
            rendered_body = template.render(context)
            logging.info("Template rendered successfully.")
            logging.info(f"Rendered Email Body (truncated): {rendered_body[:500]}")
        except jinja_exceptions.TemplateSyntaxError as e:
            logging.error(f"Template syntax error: {e.message} at line {e.lineno}")
            raise
        except jinja_exceptions.UndefinedError as e:
            logging.error(f"Undefined variable in template: {str(e)}")
            raise
        except Exception as e:
            logging.error(f"Unexpected error while rendering template: {str(e)}")
            raise

        # Step 3: Send email
        cls.send_email(subject, employee.email, rendered_body, attachments=attachments)

    @classmethod
    def interview_reminder_mail(cls, employee: Employee, schedule_interview: ScheduleInterview):
        logging.info("Inside Employee interview_reminder_mail")

        subject = "Recruitease Pro - Interview Reminder"
        business: Business = employee.business
        candidate: Candidate = schedule_interview.candidate
        attachments = []

        # Attach resume (if available)
        if candidate.resume:
            try:
                response = requests.get(candidate.resume_url)
                filename = os.path.basename(candidate.resume_url)
                mime_type, _ = mimetypes.guess_type(filename)
                mime_type = mime_type or "application/octet-stream"
                file_bytes = response.content
                attachments.append((filename, file_bytes, mime_type))
            except Exception as e:
                logging.error(f"Resume download failed: {e}")

        has_attachment = len(attachments) > 0

        # Prepare context for Jinja2
        context = {
            "request": None,
            "api_url": API_URL,
            "app_url": APP_URL,
            "candidate": candidate,
            "employee_name": employee.full_name(),
            "business": business,
            "has_attachment": has_attachment,
            "schedule_interview": schedule_interview,
        }

        # Fetch dynamic email template
        template_obj = EmailTemplate.get_or_none(
            (EmailTemplate.template_type == EmailTemplateType.EmployeeInterviewReminder) &
            (EmailTemplate.business_id == employee.business_id) &
            (EmailTemplate.status == 1)
        )
        logging.info("template_obj")
        logging.info(template_obj)
        
        # Optional fallback to global template
        if not template_obj:
            template_obj = EmailTemplate.get_or_none(
                (EmailTemplate.template_type == EmailTemplateType.EmployeeInterviewReminder) &
                (EmailTemplate.business_id >> None) &
                (EmailTemplate.status == 1)
            )

        # Raise error if no template found
        if not template_obj:
            raise Exception("Email template for Interview Reminder not found.")

        # Render email content using Jinja2
        jinja_template = Template(template_obj.email_body)
        rendered_body = jinja_template.render(context)

        # Send the email
        cls.send_email(subject, employee.email, rendered_body, attachments)

    @classmethod
    def send_esign_verification_request_email(
        cls,
        candidate: Candidate,
        employee: Employee,
        otp: str,
    ):
        """
        Sends an OTP email for e-signature verification to the candidate.
        """
        business: Business = candidate.business
        subject = f"Recruitease Pro - {business.name} - OTP Verification"

        context = {
            "employee": employee,
            "business": business,
            "otp": otp,
        }

        # Fetch the email template from the database
        template_obj = EmailTemplate.get_or_none(
            (EmailTemplate.template_type == EmailTemplateType.ESignOTPVerification)
            & (EmailTemplate.business_id == business.id)
            & (EmailTemplate.status == 1)
        )

        if not template_obj:
            raise Exception("Email template for Offer Letter not found.")

        # Render email using Jinja2
        jinja_template = Template(template_obj.email_body)
        rendered_body = jinja_template.render(context)

        # Send the email (no attachments for OTP)
        cls.send_email(subject, employee.email, rendered_body, False)
