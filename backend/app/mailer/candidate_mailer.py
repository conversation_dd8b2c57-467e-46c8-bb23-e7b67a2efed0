from app.models import (
    Business,
    Candidate,
    Employee,
    DocumentUploadLinkRequest,
    ScheduleInterview,
    EmailTrack,
    CandidateOfferLetter,
    EmailTemplate
)
from app.utils import AppTemplates
from app.config import API_URL, APP_URL
from app.mailer.base_mailer import BaseMailer
from app.utils import build_subdomain_url
from typing import Optional
import requests
from app.models.concern.enum import EmailTemplateType
from jinja2 import Template, Environment, exceptions as jinja_exceptions
import logging
from app.helper.mailer_helper import MailerHelper

class CandidateMailer(BaseMailer):
    @classmethod
    def send_document_request_link(
        cls, candidate: Candidate, employee: Employee, columns: dict
    ):
        subject = "Recruitease Pro - Request for Document Upload"
        business: Business = candidate.business
        document_request = DocumentUploadLinkRequest.create_unique_record(
            candidate_id=candidate.id, employee_id=employee.id, columns=columns
        )
        subdomain_url = build_subdomain_url(APP_URL, employee.business.subdomain)
        upload_link = f"{subdomain_url}/candidates/upload_documents?token={document_request.unique_key}"
        context = {
            "request": None,
            "api_url": API_URL,
            "app_url": APP_URL,
            "upload_link": upload_link,
            "candidate": candidate,
            "business": business,
            "document_request": document_request,
        }
        # Fetch the email template from the database
        template_obj = EmailTemplate.get_or_none(
            (EmailTemplate.template_type == EmailTemplateType.CandidateUploadDocuments)
            & (EmailTemplate.business_id == business.id)
            & (EmailTemplate.status == 1)
        )

        if not template_obj:
            raise Exception("Email template for Document Request not found.")

        # Render email using Jinja2
        jinja_template = Template(template_obj.email_body)
        rendered_body = jinja_template.render(context)
        
        cls.send_email(subject, candidate.email, rendered_body)
    @classmethod
    def interview_scheduled_mail(
        cls, candidate: Candidate, schedule_interview: ScheduleInterview
    ):
        logging.info("Inside Candidate interview_scheduled_mail")
        subject = "Recruitease Pro - Interview Scheduled"
        business: Business = candidate.business
        title = schedule_interview.status_message()
        show_comment_only: bool = schedule_interview.status.value not in (1, 2)

        password = None
        auth_exist = candidate.candidate_auth
        try:
            if not auth_exist:
                password = candidate.create_login_auth()
        except Exception as e:
            logging.warning(f"Failed to create login auth: {e}")
            password = None

        login_url = (
            f"{build_subdomain_url(APP_URL, business.subdomain)}/candidates/auth/login"
        )

        context = {
            "request": None,
            "api_url": API_URL,
            "app_url": APP_URL,
            "login_url": login_url,
            "candidate": candidate,
            "business": business,
            "title": title,
            "schedule_interview": schedule_interview,
            "show_comment_only": show_comment_only,
            "password": password,
        }

        logging.info(f"Context values: {list(context.values())}")
        logging.info(f"EmailTemplateType: {EmailTemplateType.CandidateScheduleInterviewMail}")
        logging.info(f"Business ID: {business.id}")

        # Fetch template
        template_obj = EmailTemplate.get_or_none(
            (EmailTemplate.template_type == EmailTemplateType.CandidateScheduleInterviewMail)
            & (EmailTemplate.business_id == business.id)
            & (EmailTemplate.status == 1)
        )

        if not template_obj:
            raise Exception("Email template for Interview Schedule not found.")

        logging.info("Fetched template body (truncated):")
        logging.info(template_obj.email_body[:500]) 

        # Render using Jinja2
        try:
            env = Environment()
            env.filters["to_timezone"] = MailerHelper.to_timezone
            template = env.from_string(template_obj.email_body)
            rendered_body = template.render(context)
            logging.info("Template rendered successfully.")
            logging.info(f"Rendered Email Body (truncated): {rendered_body[:500]}")
        except jinja_exceptions.TemplateSyntaxError as e:
            logging.error(f"Template syntax error: {e.message} at line {e.lineno}")
            raise
        except jinja_exceptions.UndefinedError as e:
            logging.error(f"Undefined variable in template: {str(e)}")
            raise
        except Exception as e:
            logging.error(f"Unexpected error while rendering template: {str(e)}")
            raise

        cls.send_email(subject, candidate.email, rendered_body)

    @classmethod
    def send_custom_email(cls, candidate: Candidate, subject: str, content: str):
        logging.info("Inside send_custom_email")
        business: Business = candidate.business 
        subject = f"Recruitease Pro - {subject}"
        context = {
            "request": None,
            "content": content,
        }
        # Fetch the email template from the database
        template_obj = EmailTemplate.get_or_none(
            (EmailTemplate.template_type == EmailTemplateType.CandidateCustomLayout)
            & (EmailTemplate.business_id == business.id)
            & (EmailTemplate.status == 1)
        )
        if not template_obj:
            raise Exception("Email template for Custom Email not found.")
        # Render email using Jinja2
        jinja_template = Template(template_obj.email_body)
        rendered_body = jinja_template.render(context)
        
        EmailTrack.create(
            body=str(rendered_body),
            subject=subject,
            to_email=candidate.email,
            model_name=candidate.__class__.__name__,
            model_id=candidate.id,
        )
        cls.send_email(subject, candidate.email, rendered_body)
  
    @classmethod
    def reset_password_email(cls, candidate: Candidate):
        logging.info("Inside reset_password_email")
        business: Business = candidate.business
        subject = "Recruitease Pro - Password Reset OTP"
        context = {
            "request": None,
            "candidate": candidate,
            "api_url": API_URL,
            "app_url": APP_URL,
        }
        logging.info(EmailTemplateType.CandidateForgotPassword)
        logging.info(business.id)
        # Fetch the email template from the database
        template_obj = EmailTemplate.get_or_none(
            (EmailTemplate.template_type == EmailTemplateType.CandidateForgotPassword)
            & (EmailTemplate.business_id == business.id)
            & (EmailTemplate.status == 1)
        )
        logging.info("template_obj")
        logging.info(template_obj)

        if not template_obj:
            raise Exception("Email template for Reset Password not found.")

        # Render email using Jinja2
        jinja_template = Template(template_obj.email_body)
        rendered_body = jinja_template.render(context)
        
        cls.send_email(subject, candidate.email, rendered_body)
    @classmethod
    def resend_otp_email(cls, candidate: Candidate, resend_text: str):
        business: Business = candidate.business
        subject = "Recruitease Pro - New OTP"
        context = {
            "request": None,
            "candidate": candidate,
            "api_url": API_URL,
            "app_url": APP_URL,
            "resend_text": resend_text,
        }
        # Fetch the email template from the database
        template_obj = EmailTemplate.get_or_none(
            (EmailTemplate.template_type == EmailTemplateType.CandidateResendOTP)
            & (EmailTemplate.business_id == business.id)
            & (EmailTemplate.status == 1)
        )

        if not template_obj:
            raise Exception("Email template for Reset OTP not found.")

        # Render email using Jinja2
        jinja_template = Template(template_obj.email_body)
        rendered_body = jinja_template.render(context)

        cls.send_email(subject, candidate.email, rendered_body)

    @classmethod
    def send_offer_letter_email(
        cls,
        candidate: Candidate,
        offer_letter: CandidateOfferLetter,
        subject: Optional[str],
    ):
        business = candidate.business

        subject = subject or f"Recruitease Pro - {business.name} - Offer Letter"

        # Prepare context for the email body
        context = {
            "request": None,
            "candidate": candidate,
            "business": business,
            "api_url": API_URL,
            "app_url": APP_URL,
        }

        # Fetch the email template from the database
        template_obj = EmailTemplate.get_or_none(
            (EmailTemplate.template_type == EmailTemplateType.CandidateOfferLetter)
            & (EmailTemplate.business_id == business.id)
            & (EmailTemplate.status == 1)
        )

        if not template_obj:
            raise Exception("Email template for Offer Letter not found.")

        # Render email using Jinja2
        jinja_template = Template(template_obj.email_body)
        rendered_body = jinja_template.render(context)

        # Fetch the offer letter PDF from the URL
        response = requests.get(offer_letter.letter_url)
        pdf_bytes = response.content

        attachments = [("Offer_Letter.pdf", pdf_bytes, "application/pdf")]

        # Send email with attachment
        cls.send_email(subject, candidate.email, rendered_body, attachments=attachments)
    @classmethod
    def interview_reminder_mail(cls, schedule_interview: ScheduleInterview):
        subject = "Recruitease Pro - Interview Reminder"
        candidate: Candidate = schedule_interview.candidate
        business: Business = candidate.business
        title = schedule_interview.status_message()
        show_comment_only: bool = schedule_interview.status.value not in (1, 2)

        context = {
            "request": None,
            "api_url": API_URL,
            "app_url": APP_URL,
            "candidate": candidate,
            "business": business,
            "title": title,
            "schedule_interview": schedule_interview,
            "show_comment_only": show_comment_only,
        }
        
        # Fetch the email template from the database
        template_obj = EmailTemplate.get_or_none(
            (EmailTemplate.template_type == EmailTemplateType.CandidateInterviewReminder)
            & (EmailTemplate.business_id == business.id)
            & (EmailTemplate.status == 1)
        )

        if not template_obj:
            raise Exception("Email template for Interview Reminder not found.")

        # Render email using Jinja2
        jinja_template = Template(template_obj.email_body)
        rendered_body = jinja_template.render(context)
        
        cls.send_email(subject, candidate.email, rendered_body)
