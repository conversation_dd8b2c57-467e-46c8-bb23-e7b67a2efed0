from app.models import ContactEnquery, EmailTemplate
from app.utils import AppTemplates
from app.config import API_URL, APP_URL, ENQUERY_EMAILS
from app.mailer.base_mailer import BaseMailer
from jinja2 import Template
from app.models.concern.enum import EmailTemplateType


class HomeMailer(BaseMailer):

    @classmethod
    def contact_us_email(cls, contact_enquery: ContactEnquery):
        subject = "Recruitease Pro - New Contact Us Enquiry"
        
        context = {
            "request": None,
            "contact_enquery": contact_enquery,
            "api_url": API_URL,
            "app_url": APP_URL,
        }

        # Step 1: Fetch the email template from DB (you can define a custom EmailTemplateType for Contact Enquiry)
        template_obj = EmailTemplate.get_or_none(
            (EmailTemplate.template_type == EmailTemplateType.ContactEnquiry) &  # You must define this Enum
            (EmailTemplate.status == 1) & 
            (EmailTemplate.is_default == True)
        )

        # Step 2: Raise error if no template is found
        if not template_obj:
            raise Exception("Email template for Contact Enquiry not found.")

        # Step 3: Render email body using Jinja2
        jinja_template = Template(template_obj.email_body)
        rendered_body = jinja_template.render(context)

        # Step 4: Send the email
        cls.send_email(subject, ENQUERY_EMAILS, rendered_body, send_to_primary=True)
