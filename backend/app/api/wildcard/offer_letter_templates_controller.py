from fastapi import APIRouter, Depends, HTTPException, status, Body, Query
from app.schema import SuccessResponse, PaginationResponse
from app.models import Business, OfferLetterTemplate, Employee
from app.validations import StringValidate, IntegerValidate
from app.exceptions import RecordNotFoundException
from app.helper import Wildcard<PERSON><PERSON><PERSON>elper
from peewee import fn
from typing import Optional
import logging
import datetime

router = APIRouter(
    prefix="/offer_letter_templates",
    tags=["Wildcard Offer Letter Template API"],
    dependencies=[
        Depends(WildcardAuthHelper.get_current_employee),
        Depends(WildcardAuthHelper.get_current_auth_token),
    ],
)


def get_offer_letter_template(
    id: int,
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Dependency to get and validate offer letter template access.
    """
    try:
        offer_letter_template = OfferLetterTemplate.get_by_id(id)
        if offer_letter_template.is_default == 0 and offer_letter_template.business_id != business.id:
            raise RecordNotFoundException(message="Offer Letter Template does not exist")
        return offer_letter_template
    except OfferLetterTemplate.DoesNotExist:
        raise RecordNotFoundException(message="Offer Letter Template does not exist")


@router.get("/", summary="Get all offer letter templates", response_model=PaginationResponse)
def get_all_offer_letter_templates(
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    search: Optional[str] = Query(None),
):
    """
    Retrieve and return all offer letter templates with pagination.
    
    Returns:
        PaginationResponse: A JSON response containing the list of offer letter templates.
    Raises:
        HTTPException: If any error occurs during processing.
    """
    try:
        query = OfferLetterTemplate.select().where(
            (OfferLetterTemplate.business_id == business.id) | 
            (OfferLetterTemplate.is_default == 1)
        )
        
        if search:
            query = query.where(
                (OfferLetterTemplate.template_name.contains(search)) |
                (OfferLetterTemplate.position.contains(search)) |
                (OfferLetterTemplate.department.contains(search))
            )
        
        total_count = query.count()
        
        templates = query.order_by(OfferLetterTemplate.created_at.desc()).paginate(page, limit)
        templates_data = [template.info() for template in templates]
        
        return PaginationResponse(
            message="Offer letter templates fetched successfully.",
            data={
                "page": page,
                "limit": limit,
                "count": total_count,
                "rows": templates_data
            }
        )
    except Exception as e:
        logging.exception("Error fetching offer letter templates")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )


@router.post("", summary="Create offer letter template", response_model=SuccessResponse)
def create_offer_letter_template(
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    body: dict = Body(...),
):
    """
    Creates a new offer letter template.
    Args:
        template_data: Offer letter template data to be added.
    Returns:
        SuccessResponse: A JSON response containing the message and info of the created template.
    Raises:
        HTTPException: If any error occurs during processing.
    """
    try:
        template_name = StringValidate(
            body.get("template_name"),
            field="Template Name",
            required=True,
            max_length=255,
            strip=True,
        )
        template_content = StringValidate(
            body.get("template_content"), 
            field="Template Content", 
            required=True, 
            strip=True
        )
        position = StringValidate(
            body.get("position"),
            field="Position",
            required=False,
            max_length=255,
            strip=True,
        )
        department = StringValidate(
            body.get("department"),
            field="Department",
            required=False,
            max_length=255,
            strip=True,
        )
        template_type = StringValidate(
            body.get("template_type", "Standard"),
            field="Template Type",
            required=False,
            strip=True,
        )

        # Check if template already exists with the provided name
        existing_template = OfferLetterTemplate.get_or_none(
            OfferLetterTemplate.template_name == template_name,
            OfferLetterTemplate.business_id == business.id
        )
        if existing_template:
            raise ValueError("Template with this name already exists")

        offer_letter_template = OfferLetterTemplate.create(
            template_name=template_name,
            template_content=template_content,
            position=position,
            department=department,
            template_type=template_type,
            business_id=business.id,
            created_by_id=current_employee.id
        )

        return SuccessResponse(
            message="Offer letter template created successfully.", 
            data=offer_letter_template.info()
        )
    except Exception as e:
        logging.exception("Error creating offer letter template")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, 
            detail=str(e)
        )


@router.get("/{id}", summary="Get specific offer letter template", response_model=SuccessResponse)
def get_specific_offer_letter_template(
    offer_letter_template: OfferLetterTemplate = Depends(get_offer_letter_template),
):
    """
    Retrieve a specific offer letter template.
    Args:
        id: Template ID to fetch
    Returns:
        SuccessResponse: A JSON response containing the template data.
    Raises:
        HTTPException: If the template is not found or any other error occurs.
    """
    try:
        return SuccessResponse(
            message="Offer letter template fetched successfully.", 
            data=offer_letter_template.info()
        )
    except Exception as e:
        logging.exception("Error fetching offer letter template")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, 
            detail=str(e)
        )


@router.put("/{id}", summary="Update offer letter template", response_model=SuccessResponse)
def update_offer_letter_template(
    offer_letter_template: OfferLetterTemplate = Depends(get_offer_letter_template),
    body: dict = Body(...),
):
    """
    Update an existing offer letter template.
    Args:
        id: Template ID to update
        body: Updated template data
    Returns:
        SuccessResponse: A JSON response containing the updated template data.
    Raises:
        HTTPException: If the template is not found or any other error occurs.
    """
    try:
        if "template_name" in body:
            template_name = StringValidate(
                body.get("template_name"),
                field="Template Name",
                required=True,
                max_length=255,
                strip=True,
            )
            offer_letter_template.template_name = template_name

        if "template_content" in body:
            template_content = StringValidate(
                body.get("template_content"), 
                field="Template Content", 
                required=True, 
                strip=True
            )
            offer_letter_template.template_content = template_content

        if "position" in body:
            position = StringValidate(
                body.get("position"),
                field="Position",
                required=False,
                max_length=255,
                strip=True,
            )
            offer_letter_template.position = position

        if "department" in body:
            department = StringValidate(
                body.get("department"),
                field="Department",
                required=False,
                max_length=255,
                strip=True,
            )
            offer_letter_template.department = department

        if "template_type" in body:
            template_type = StringValidate(
                body.get("template_type"),
                field="Template Type",
                required=False,
                strip=True,
            )
            offer_letter_template.template_type = template_type

        offer_letter_template.save()

        return SuccessResponse(
            message="Offer letter template updated successfully.", 
            data=offer_letter_template.info()
        )
    except Exception as e:
        logging.exception("Error updating offer letter template")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, 
            detail=str(e)
        )


@router.put("/{id}/status", summary="Update offer letter template status", response_model=SuccessResponse)
def update_offer_letter_template_status(
    offer_letter_template: OfferLetterTemplate = Depends(get_offer_letter_template),
    body: dict = Body(...),
):
    """
    Update the status of an offer letter template.
    Args:
        id: Template ID to update
        body: Status data
    Returns:
        SuccessResponse: A JSON response containing the updated template data.
    Raises:
        HTTPException: If the template is not found or any other error occurs.
    """
    try:
        status_value = IntegerValidate(
            body.get("status"),
            field="Status",
            required=True,
            min_value=0,
            max_value=1,
        )
        
        offer_letter_template.status = status_value
        offer_letter_template.save()

        return SuccessResponse(
            message="Offer letter template status updated successfully.", 
            data=offer_letter_template.info()
        )
    except Exception as e:
        logging.exception("Error updating offer letter template status")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, 
            detail=str(e)
        )


@router.delete("/{id}", summary="Delete offer letter template", response_model=SuccessResponse)
def delete_offer_letter_template(
    offer_letter_template: OfferLetterTemplate = Depends(get_offer_letter_template),
):
    """
    Delete an offer letter template.
    Args:
        id: Template ID to delete
    Returns:
        SuccessResponse: A JSON response confirming deletion.
    Raises:
        HTTPException: If the template is not found or any other error occurs.
    """
    try:
        # Don't allow deletion of default templates
        if offer_letter_template.is_default == 1:
            raise ValueError("Cannot delete default templates")
        
        offer_letter_template.delete_instance()

        return SuccessResponse(
            message="Offer letter template deleted successfully."
        )
    except Exception as e:
        logging.exception("Error deleting offer letter template")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, 
            detail=str(e)
        )
