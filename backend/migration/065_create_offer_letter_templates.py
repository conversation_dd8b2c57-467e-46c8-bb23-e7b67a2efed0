"""
Create offer letter templates table
"""

import peewee as pw
from playhouse.migrate import <PERSON><PERSON><PERSON>


def migrate(migrator: Mi<PERSON><PERSON>, database: pw.Database, *, fake=False):
    sql = """
    CREATE TABLE offer_letter_templates (
        id INT NOT NULL PRIMARY KEY AUTO_INCREMENT,
        template_name <PERSON><PERSON>HA<PERSON>(255) NOT NULL,
        template_content TEXT NOT NULL,
        position VARCHAR(255) NULL,
        department VARCHAR(255) NULL,
        status SMALLINT DEFAULT 1,
        is_default SMALLINT DEFAULT 0,
        template_type ENUM('Standard', 'Executive', 'Internship', 'Contract', 'Remote') DEFAULT 'Standard',
        business_id BIGINT NULL,
        created_by_id BIGINT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIG<PERSON> KEY (business_id) REFERENCES businesses(id),
        FOREIG<PERSON> KEY (created_by_id) REFERENCES employees(id)
    );
    """
    
    if not fake:
        database.execute_sql(sql)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    sql = "DROP TABLE IF EXISTS offer_letter_templates;"
    
    if not fake:
        database.execute_sql(sql)
